"""
Database migration script to add metadata fields to documents table.

This script adds service_name, software_menus, and issue_type columns to the documents table.
"""

import logging
import os
import sys
from sqlalchemy import text

# Add the parent directory to the path so we can import the database modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from app.database.db import get_db, engine

logger = logging.getLogger(__name__)

def run_migration():
    """
    Run the migration to add metadata fields to documents table.
    """
    try:
        # Add the new columns
        with engine.connect() as connection:
            # Check if the columns exist in MySQL
            for column in ['service_name', 'software_menus', 'issue_type']:
                result = connection.execute(text(
                    f"SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'ragchatbot' "
                    f"AND table_name = 'documents' AND column_name = '{column}'"
                ))
                column_exists = result.scalar() > 0
                
                if not column_exists:
                    # Add the column
                    logger.info(f"Adding {column} column to documents table...")
                    connection.execute(text(f"ALTER TABLE documents ADD COLUMN {column} VARCHAR(255)"))
                    connection.commit()
                    logger.info(f"Successfully added {column} column to documents table")
                else:
                    logger.info(f"{column} column already exists in documents table")
                
        logger.info("Successfully added all metadata columns to documents table")
        
    except Exception as e:
        logger.error(f"Error running migration: {str(e)}")
        raise

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Run the migration
    run_migration()
