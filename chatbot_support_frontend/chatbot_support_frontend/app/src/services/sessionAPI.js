import { API_BASE_URL, handleResponse, fetchWithAuth, getAuthToken } from './api';

// Session management API
export const sessionAPI = {
  // Refresh the current session - using the existing /users/me endpoint
  refreshSession: async () => {
    console.log('Refreshing session...');
    try {
      // Use the existing /users/me endpoint to validate the token
      const response = await fetchWithAuth(`${API_BASE_URL}/users/me`, {
        method: 'GET',
        credentials: 'include', // Include credentials
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      console.log('Refresh response status:', response.status);
      return handleResponse(response);
    } catch (error) {
      console.error('Network error during session refresh:', error);
      // Don't reject the promise for network errors to prevent app crashes
      return { success: false, error: 'Network error during session refresh' };
    }
  },

  // Logout - since there's no logout endpoint, we'll just handle it client-side
  logout: async () => {
    try {
      // Since there's no logout endpoint yet, we'll just return success
      // In a real implementation, you would call the backend to invalidate the token
      console.log('Logging out client-side only (no server endpoint yet)');
      return { success: true };
    } catch (error) {
      console.error('Error during logout:', error);
      // Even if the API call fails, we should still clear local storage
      return { success: true };
    }
  },
};
