import React, { useState, useRef, useEffect, useMemo } from 'react';
import { BellIcon, CheckIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useNotification } from '../../contexts/NotificationContext';
import { useTheme } from '../../contexts/ThemeContext';
import './NotificationBell.css';

const NotificationBell = () => {
  const theme = useTheme();
  const {
    notifications,
    unreadCount,
    loading,
    hasNewNotifications,
    fetchNotifications,
    fetchUnreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    resetNewNotificationsFlag,
    checkForNewNotifications
  } = useNotification();

  // Filter out duplicate notifications
  const uniqueNotifications = useMemo(() => {
    const seen = new Map();
    return notifications.filter(notification => {
      // Create a unique key based on document_id and type
      const key = `${notification.document_id}-${notification.type}`;

      // If we haven't seen this key before, keep the notification
      if (!seen.has(key)) {
        seen.set(key, true);
        return true;
      }

      return false;
    });
  }, [notifications]);

  // Calculate unique unread count
  const uniqueUnreadCount = useMemo(() => {
    return uniqueNotifications.filter(notification => !notification.is_read).length;
  }, [uniqueNotifications]);

  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get notification type icon
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'document_added':
        return '📄';
      case 'document_deleted':
        return '🗑️';
      case 'system_reset':
        return '🔄';
      case 'user_added':
        return '👤';
      default:
        return '🔔';
    }
  };

  // Toggle dropdown
  const toggleDropdown = () => {
    if (!isOpen) {
      // Check for new notifications when opening the bell icon
      if (checkForNewNotifications) {
        checkForNewNotifications();
      }

      // Fetch notifications only when opening the dropdown
      fetchNotifications(true); // Include read notifications when opening
      resetNewNotificationsFlag(); // Reset the new notifications flag
    }
    setIsOpen(!isOpen);
  };

  // Handle mark as read
  const handleMarkAsRead = async (id, e) => {
    e.stopPropagation();
    try {
      await markAsRead(id);
      // Refresh notifications after marking as read
      fetchNotifications(true);
      // Update unread count
      fetchUnreadCount();
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Handle mark all as read
  const handleMarkAllAsRead = async (e) => {
    e.stopPropagation();
    try {
      await markAllAsRead();
      // Refresh notifications after marking all as read
      fetchNotifications(true);
      // Update unread count
      fetchUnreadCount();
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // Handle delete
  const handleDelete = async (id, e) => {
    e.stopPropagation();
    try {
      await deleteNotification(id);
      // Refresh notifications after deleting
      fetchNotifications(true);
      // Update unread count
      fetchUnreadCount();
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="notification-bell-container" ref={dropdownRef}>
      <button
        className={`notification-bell-button ${hasNewNotifications ? 'has-new-notifications' : ''}`}
        onClick={toggleDropdown}
        aria-label="Notifications"
      >
        <BellIcon className={`notification-bell-icon ${hasNewNotifications ? 'pulse' : ''}`} />
        {uniqueUnreadCount > 0 && (
          <span className="notification-badge">{uniqueUnreadCount}</span>
        )}
      </button>

      {isOpen && (
        <div className={`notification-dropdown ${theme.darkMode ? 'dark' : ''}`}>
          <div className="notification-header">
            <h3>Notifications</h3>
            {uniqueNotifications.length > 0 && (
              <button
                className="mark-all-read-button"
                onClick={handleMarkAllAsRead}
                disabled={loading}
              >
                Mark all as read
              </button>
            )}
          </div>

          <div className="notification-list">
            {loading && <div className="notification-loading">Loading...</div>}

            {!loading && uniqueNotifications.length === 0 && (
              <div className="notification-empty">No notifications</div>
            )}

            {!loading && uniqueNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`notification-item ${notification.is_read ? 'read' : 'unread'}`}
              >
                <div className="notification-icon">
                  {getNotificationIcon(notification.type)}
                </div>
                <div className="notification-content">
                  <div className="notification-title">{notification.title}</div>
                  <div className="notification-message">{notification.message}</div>
                  <div className="notification-meta">
                    <span className="notification-time">{formatDate(notification.created_at)}</span>
                    {notification.creator_name && (
                      <span className="notification-creator">by {notification.creator_name}</span>
                    )}
                  </div>
                </div>
                <div className="notification-actions">
                  {!notification.is_read && (
                    <button
                      className="notification-action-button"
                      onClick={(e) => handleMarkAsRead(notification.id, e)}
                      title="Mark as read"
                    >
                      <CheckIcon className="notification-action-icon" />
                    </button>
                  )}
                  <button
                    className="notification-action-button"
                    onClick={(e) => handleDelete(notification.id, e)}
                    title="Delete"
                  >
                    <TrashIcon className="notification-action-icon" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationBell;
