import React, { createContext, useState, useContext, useEffect } from 'react';
import { useAuth } from './AuthContext';
import { userAPI, groupAPI } from '../services/api';

// Create user context
const UserContext = createContext();

// User provider component
export const UserProvider = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [users, setUsers] = useState([]);
  const [groups, setGroups] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch users and groups when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      // Always fetch groups as they're needed for UI
      fetchGroups();

      // Only fetch users if admin
      if (user?.role === 'admin') {
        fetchUsers();
      }
    }
  }, [isAuthenticated, user]);

  // Fetch all users
  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await userAPI.getAllUsers();
      console.log('Users API response:', response);

      // Transform the users data into an array format
      let usersArray = [];

      if (Array.isArray(response)) {
        // If response is already an array, use it directly
        usersArray = response;
      } else if (response.users && Array.isArray(response.users)) {
        // If users property is an array, use it
        usersArray = response.users;
      } else if (response.users && typeof response.users === 'object') {
        // If users is an object with user data, transform it to array
        usersArray = Object.keys(response.users).map(key => {
          const user = response.users[key];
          return {
            id: user.id || key,
            username: user.username || 'Unknown',
            email: user.email || '',
            role: user.role || 'user',
            groups: user.groups || []
          };
        });
      } else if (typeof response === 'object' && !response.users) {
        // If response is a direct object of users
        usersArray = Object.keys(response).map(key => {
          const user = response[key];
          return {
            id: user.id || key,
            username: user.username || 'Unknown',
            email: user.email || '',
            role: user.role || 'user',
            groups: user.groups || []
          };
        });
      }

      console.log('Transformed users data:', usersArray);
      setUsers(usersArray);

      return { success: true, data: usersArray };
    } catch (error) {
      console.error('Error fetching users:', error);
      setError('Failed to fetch users: ' + (error.message || error));
      return { success: false, error: error.message || error };
    } finally {
      setLoading(false);
    }
  };

  // Fetch all groups
  const fetchGroups = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await groupAPI.getAllGroups();
      console.log('Groups API response:', response);

      // Transform the groups data into an array format
      let groupsArray = [];

      if (Array.isArray(response)) {
        // If response is already an array, use it directly
        groupsArray = response;
      } else if (response.groups && Array.isArray(response.groups)) {
        // If groups property is an array, use it
        groupsArray = response.groups;
      } else if (response.groups && typeof response.groups === 'object') {
        // If groups is an object with group data, transform it to array
        groupsArray = Object.keys(response.groups).map(key => {
          const group = response.groups[key];
          return {
            id: group.id || key,
            name: group.name || 'Unnamed Group',
            description: group.description || '',
            members: group.members || []
          };
        });
      } else if (typeof response === 'object' && !response.groups) {
        // If response is a direct object of groups
        groupsArray = Object.keys(response).map(key => {
          const group = response[key];
          return {
            id: group.id || key,
            name: group.name || 'Unnamed Group',
            description: group.description || '',
            members: group.members || []
          };
        });
      }

      console.log('Transformed groups data:', groupsArray);
      setGroups(groupsArray);

      // Update user's groups in localStorage if needed
      try {
        const userData = localStorage.getItem('userData');
        if (userData) {
          const parsedUserData = JSON.parse(userData);

          // If we have user data and groups from the API response
          if (user && user.id && parsedUserData.id === user.id) {
            // For regular users, the groups array contains their groups
            if (user.role !== 'admin' && groupsArray.length > 0) {
              // Extract group IDs
              const groupIds = groupsArray.map(group => group.id);
              console.log('Updating user groups in localStorage from API response:', groupIds);

              // Update user's groups in localStorage
              parsedUserData.groups = groupIds;
              localStorage.setItem('userData', JSON.stringify(parsedUserData));

              // Also update the user in context
              if (user && !user.groups || user.groups.length === 0) {
                user.groups = groupIds;
              }
            }
            // For admins or if no groups were found, use existing logic
            else if (user.groups && user.groups.length > 0 &&
                (!parsedUserData.groups || !parsedUserData.groups.length)) {
              console.log('Updating user groups in localStorage from context:', user.groups);
              parsedUserData.groups = user.groups;
              localStorage.setItem('userData', JSON.stringify(parsedUserData));
            }
          }
        }
      } catch (localStorageError) {
        console.error('Error updating localStorage with groups:', localStorageError);
      }

      return { success: true, data: groupsArray };
    } catch (error) {
      console.error('Error fetching groups:', error);
      setError('Failed to fetch groups: ' + (error.message || error));
      return { success: false, error: error.message || error };
    } finally {
      setLoading(false);
    }
  };

  // Create a new user
  const createUser = async (userData) => {
    try {
      setLoading(true);
      setError(null);

      console.log('Creating user with data:', userData);

      // Prepare the data for the API
      const apiData = {
        username: userData.username,
        password: userData.password,
        role: userData.role || 'user'
      };

      // Add email if provided
      if (userData.email) {
        apiData.email = userData.email;
      }

      // Add groups if provided
      if (userData.groups && userData.groups.length > 0) {
        apiData.groups = userData.groups;
      }

      const response = await userAPI.createUser(apiData);
      console.log('Create user response:', response);

      // Always refresh the user list after attempting to create
      await fetchUsers();

      // Check if the response indicates success
      if (response && (response.user_id || response.id || response.username)) {
        console.log('User creation successful');
        return { success: true, userId: response.user_id || response.id };
      } else {
        console.log('User creation response unclear, but refreshing list to check');
        // Even if response is unclear, the user might have been created
        // The refresh above will show the new user if it was created
        return { success: true, userId: null };
      }
    } catch (error) {
      console.error('Error creating user:', error);

      // Always refresh the user list even on error, in case the user was actually created
      try {
        await fetchUsers();
        console.log('Refreshed user list after error');
      } catch (refreshError) {
        console.error('Error refreshing user list after creation error:', refreshError);
      }

      // Check if this is a network/proxy error but user might have been created
      if (error.message && (
        error.message.includes('Server returned HTML instead of JSON') ||
        error.message.includes('Invalid response format') ||
        error.message.includes('Failed to fetch')
      )) {
        console.log('Network/proxy error detected, user might have been created successfully');
        // Return success since the user list refresh will show if it was created
        return { success: true, userId: null, warning: 'User may have been created. Please check the user list.' };
      }

      setError('Failed to create user: ' + (error.message || error));
      return { success: false, error: typeof error === 'string' ? error : 'Failed to create user' };
    } finally {
      setLoading(false);
    }
  };

  // Update a user
  const updateUser = async (userId, userData) => {
    try {
      setLoading(true);
      setError(null);

      console.log('Updating user with ID:', userId, 'Data:', userData);

      // Prepare the data for the API
      const apiData = {
        username: userData.username
      };

      // Only include password if provided (for password changes)
      if (userData.password) {
        apiData.password = userData.password;
      }

      // Add email if provided
      if (userData.email) {
        apiData.email = userData.email;
      }

      // Add role if provided
      if (userData.role) {
        apiData.role = userData.role;
      }

      // Add groups if provided
      if (userData.groups && userData.groups.length > 0) {
        apiData.groups = userData.groups;
      }

      const response = await userAPI.updateUser(userId, apiData);
      console.log('Update user response:', response);

      // Refresh the user list
      await fetchUsers();

      return { success: true, data: response };
    } catch (error) {
      console.error('Error updating user:', error);

      // Special handling for "Failed to fetch" errors after an update operation
      if (error instanceof TypeError && error.message === 'Failed to fetch') {
        console.warn('Caught "Failed to fetch" error during user update. Attempting to re-fetch users to verify update.');
        try {
          await fetchUsers(); // Re-fetch users to see if the update actually occurred
          console.log('User list refreshed after "Failed to fetch" during update. Assuming success.');
          return { success: true, message: 'User updated successfully (network error during response).' };
        } catch (refreshError) {
          console.error('Failed to refresh user list after "Failed to fetch" during update:', refreshError);
          setError('Failed to update user: ' + (refreshError.message || refreshError));
          return { success: false, error: typeof refreshError === 'string' ? refreshError : 'Failed to update user' };
        }
      }

      setError('Failed to update user: ' + (error.message || error));
      return { success: false, error: typeof error === 'string' ? error : 'Failed to update user' };
    } finally {
      setLoading(false);
    }
  };

  // Delete a user
  const deleteUser = async (userId) => {
    try {
      setLoading(true);
      setError(null);

      console.log('Deleting user with ID:', userId);

      const response = await userAPI.deleteUser(userId);
      console.log('Delete user response:', response);

      // Refresh the user list
      await fetchUsers();

      return { success: true, data: response };
    } catch (error) {
      console.error('Error deleting user:', error);

      // Special handling for "Failed to fetch" errors after a delete operation
      // This often happens if the backend successfully deletes but closes the connection abruptly,
      // or if there's a transient network issue *after* the backend processed the request.
      if (error instanceof TypeError && error.message === 'Failed to fetch') {
        console.warn('Caught "Failed to fetch" error during user deletion. Attempting to re-fetch users to verify deletion.');
        try {
          await fetchUsers(); // Re-fetch users to see if the deletion actually occurred
          // If fetchUsers succeeds and the user is no longer in the list, consider it a success
          // We don't have direct access to the updated users list here, but the subsequent render
          // will reflect the change if fetchUsers was successful.
          console.log('User list refreshed after "Failed to fetch" during deletion. Assuming success.');
          return { success: true, message: 'User deleted successfully (network error during response).' };
        } catch (refreshError) {
          console.error('Failed to refresh user list after "Failed to fetch" during deletion:', refreshError);
          setError('Failed to delete user: ' + (refreshError.message || refreshError));
          return { success: false, error: typeof refreshError === 'string' ? refreshError : 'Failed to delete user' };
        }
      }

      setError('Failed to delete user: ' + (error.message || error));
      return { success: false, error: typeof error === 'string' ? error : 'Failed to delete user' };
    } finally {
      setLoading(false);
    }
  };

  // Create a new group
  const createGroup = async (groupData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await groupAPI.createGroup(groupData);

      // Refresh group list
      await fetchGroups();

      return { success: true, groupId: response.group_id };
    } catch (error) {
      console.error('Error creating group:', error);
      setError('Failed to create group');
      return { success: false, error: typeof error === 'string' ? error : 'Failed to create group' };
    } finally {
      setLoading(false);
    }
  };

  // Update a group
  const updateGroup = async (groupId, groupData) => {
    try {
      setLoading(true);
      setError(null);

      console.log('Updating group with ID:', groupId, 'Data:', groupData);

      // Use the PUT endpoint for updating groups
      const updateData = {
        name: groupData.name,
        description: groupData.description || '',
        members: groupData.members || []
      };

      try {
        const updateResult = await groupAPI.updateGroup(groupId, updateData);
        console.log('Update group result:', updateResult);

        // Refresh group list
        await fetchGroups();

        return { success: true, groupId: groupId };
      } catch (apiError) {
        console.error('API error updating group:', apiError);

        // Check if the error is due to duplicate group name
        if (typeof apiError === 'string' && apiError.includes('already exists')) {
          setError('A group with this name already exists. Please choose a different name.');
          return { success: false, error: 'A group with this name already exists' };
        }

        // Handle other API errors
        setError('Failed to update group: ' + (apiError.message || apiError));
        return { success: false, error: typeof apiError === 'string' ? apiError : 'Failed to update group' };
      }
    } catch (error) {
      console.error('Error in updateGroup function:', error);
      setError('Failed to update group: ' + (error.message || error));
      return { success: false, error: typeof error === 'string' ? error : 'Failed to update group' };
    } finally {
      setLoading(false);
    }
  };

  // Delete a group
  const deleteGroup = async (groupId) => {
    try {
      setLoading(true);
      setError(null);

      await groupAPI.deleteGroup(groupId);

      // Refresh group list
      await fetchGroups();

      return { success: true };
    } catch (error) {
      console.error('Error deleting group:', error);
      setError('Failed to delete group');
      return { success: false, error: typeof error === 'string' ? error : 'Failed to delete group' };
    } finally {
      setLoading(false);
    }
  };

  // Add user to group
  const addUserToGroup = async (groupId, userId) => {
    try {
      setLoading(true);
      setError(null);

      await groupAPI.addUserToGroup(groupId, userId);

      // Refresh lists
      await fetchUsers();
      await fetchGroups();

      return { success: true };
    } catch (error) {
      console.error('Error adding user to group:', error);
      setError('Failed to add user to group');
      return { success: false, error: typeof error === 'string' ? error : 'Failed to add user to group' };
    } finally {
      setLoading(false);
    }
  };

  // Remove user from group
  const removeUserFromGroup = async (groupId, userId) => {
    try {
      setLoading(true);
      setError(null);

      await groupAPI.removeUserFromGroup(groupId, userId);

      // Refresh lists
      await fetchUsers();
      await fetchGroups();

      return { success: true };
    } catch (error) {
      console.error('Error removing user from group:', error);
      setError('Failed to remove user from group');
      return { success: false, error: typeof error === 'string' ? error : 'Failed to remove user from group' };
    } finally {
      setLoading(false);
    }
  };

  // Context value
  const value = {
    users,
    groups,
    loading,
    error,
    fetchUsers,
    fetchGroups,
    createUser,
    updateUser,
    deleteUser,
    createGroup,
    updateGroup,
    deleteGroup,
    addUserToGroup,
    removeUserFromGroup,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};

// Custom hook to use user context
export const useUser = () => {
  const context = useContext(UserContext);

  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }

  return context;
};
