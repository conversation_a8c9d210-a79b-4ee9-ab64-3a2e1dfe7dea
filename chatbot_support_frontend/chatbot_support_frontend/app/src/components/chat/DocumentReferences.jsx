import React from 'react';
import { ArrowTopRightOnSquareIcon as ExternalLinkIcon, DocumentTextIcon } from '@heroicons/react/24/outline';

/**
 * Component to display document references in chat messages
 *
 * @param {Object} props
 * @param {Array} props.references - Array of document references
 * @param {boolean} props.darkMode - Whether dark mode is enabled
 */
const DocumentReferences = ({ references = [], darkMode = false }) => {
  // If no references, don't render anything
  if (!references || references.length === 0) {
    return null;
  }

  // Log references for debugging
  // console.log('Document references:', references); // Logging can be verbose; enable if needed for debugging

  // Function to format page number display for grouped references
  const formatPageDisplay = (pages) => {
    if (!pages || pages.length === 0) {
      return '';
    }
    const sortedPages = [...new Set(pages)].sort((a, b) => a - b); // Sort and unique pages
    if (sortedPages.length === 1) {
      return ` (Page ${sortedPages[0]})`;
    }
    return ` (Pages ${sortedPages.join(', ')})`;
  };

  // Function to get a clean document name (without extension)
  const getDocumentName = (filename) => {
    if (!filename) return "Unknown Document";

    // Remove file extension
    const nameParts = filename.split('.');
    if (nameParts.length > 1) {
      nameParts.pop(); // Remove the last part (extension)
    }

    // Join back and capitalize words
    return nameParts.join('.')
      .split(/[_-]/) // Split by underscores or hyphens
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Group references by document_id
  const groupedReferences = {};
  if (Array.isArray(references)) {
    references.forEach(ref => {
      const id = ref.document_id || ref.filename; // Use filename as fallback if document_id is missing
      if (id) {
        if (!groupedReferences[id]) {
          groupedReferences[id] = {
            document_id: ref.document_id,
            filename: ref.filename,
            s3_key: ref.s3_key,
            url: ref.url,
            pages: new Set(), // Use a Set to store unique page numbers
            score: ref.score // Keep one score, or average if needed (for now, just one)
          };
        }
        if (ref.page !== null && ref.page !== undefined) {
          groupedReferences[id].pages.add(ref.page);
        }
      }
    });
  }

  // Convert grouped references to an array for mapping
  let uniqueReferences = Object.values(groupedReferences);

  // Display only the first document if there are any
  if (uniqueReferences.length > 0) {
    uniqueReferences = [uniqueReferences[0]];
  } else {
    return null; // No references to display
  }

  return (
    <div className="document-references" style={{
      marginTop: '0.75rem',
      borderTop: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`,
      paddingTop: '0.5rem',
      fontSize: '0.75rem',
      color: darkMode ? '#9CA3AF' : '#6B7280'
    }}>
      <div style={{ marginBottom: '0.25rem', fontWeight: '500' }}>
        Sources:
      </div>
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem' }}>
        {uniqueReferences.map((ref, index) => (
          <a
            key={`${ref.document_id || ref.filename}-${index}`} // Use document_id or filename for key
            href={ref.url || '#'}
            onClick={(e) => {
              if (!ref.url) {
                e.preventDefault();
                console.warn('Document URL not available:', ref);
                alert(`Document URL not available for: ${getDocumentName(ref.filename)}`);
              }
            }}
            target="_blank"
            rel="noopener noreferrer"
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '0.25rem 0.5rem',
              borderRadius: '0.25rem',
              backgroundColor: darkMode ? '#374151' : '#F3F4F6',
              border: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`,
              maxWidth: '100%',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              textDecoration: 'none',
              color: 'inherit',
              cursor: ref.url ? 'pointer' : 'not-allowed',
              transition: 'all 0.2s ease',
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
            }}
            onMouseEnter={(e) => {
              if (ref.url) {
                e.currentTarget.style.backgroundColor = darkMode ? '#4B5563' : '#E5E7EB';
                e.currentTarget.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
              }
            }}
            onMouseLeave={(e) => {
              if (ref.url) {
                e.currentTarget.style.backgroundColor = darkMode ? '#374151' : '#F3F4F6';
                e.currentTarget.style.boxShadow = '0 1px 2px rgba(0, 0, 0, 0.05)';
              }
            }}
            title={ref.url ? `Open: ${getDocumentName(ref.filename)}${formatPageDisplay(Array.from(ref.pages))} (URL: ${ref.url})` : `Document URL not available for: ${getDocumentName(ref.filename)}`}
          >
            <DocumentTextIcon style={{
              width: '1rem',
              height: '1rem',
              marginRight: '0.5rem',
              color: ref.url ? (darkMode ? '#60A5FA' : '#2563EB') : (darkMode ? '#9CA3AF' : '#9CA3AF')
            }} />
            <span style={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              fontWeight: '500',
              display: 'flex',
              maxWidth: 'calc(100% - 2rem)'
            }}>
              {/* Display only the document name and grouped pages */}
              {getDocumentName(ref.filename)}{formatPageDisplay(Array.from(ref.pages))}
            </span>
            <ExternalLinkIcon style={{
              width: '0.875rem',
              height: '0.875rem',
              marginLeft: '0.5rem',
              color: ref.url ? (darkMode ? '#60A5FA' : '#2563EB') : (darkMode ? '#9CA3AF' : '#9CA3AF')
            }} />
          </a>
        ))}
      </div>
    </div>
  );
};

export default DocumentReferences;
