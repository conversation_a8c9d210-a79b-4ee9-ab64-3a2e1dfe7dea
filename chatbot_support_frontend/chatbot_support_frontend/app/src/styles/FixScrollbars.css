/* Fix for scrollbar issues in admin interface */

/* Global fixes */
html, body {
  overflow-x: hidden; /* Prevent horizontal scrolling at the document level */
}

/* Admin layout fixes */
.app-container {
  display: flex;
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0; /* Important: allows flex items to shrink below content size */
  max-width: 100%; /* Ensure content doesn't exceed viewport width */
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

/* Main content area */
main {
  flex: 1;
  padding: 1rem;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  max-width: 100%; /* Ensure content doesn't exceed viewport width */
}

/* Admin page fixes */
.admin-page {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

/* Table container fixes */
.table-container {
  width: 100%;
  overflow-x: auto; /* Allow horizontal scrolling only within tables */
  max-width: 100%; /* Ensure content doesn't exceed viewport width */
}

/* Search bar fixes */
.search-bar {
  max-width: 100%; /* Ensure search bar doesn't exceed viewport width */
  width: 100%;
}

/* Chat container fixes */
.chat-container.admin-page {
  overflow: hidden !important; /* Override any other styles */
  max-width: 100%;
}

.admin-page .chat-main {
  overflow: hidden !important; /* Override any other styles */
  max-width: 100%;
}

.admin-page .chat-content {
  overflow: hidden !important; /* Override any other styles */
  max-width: 100%;
}

.admin-page .chat-messages {
  overflow-y: auto !important; /* Allow vertical scrolling */
  overflow-x: hidden !important; /* Prevent horizontal scrolling */
  max-width: 100%;
}

/* Fix for modals */
.modal-content {
  max-width: 100%;
  overflow-x: hidden;
}

/* Fix for tables */
table {
  width: 100%;
  max-width: 100%;
}

/* Fix for inputs and form elements */
input, select, textarea {
  max-width: 100%;
}
