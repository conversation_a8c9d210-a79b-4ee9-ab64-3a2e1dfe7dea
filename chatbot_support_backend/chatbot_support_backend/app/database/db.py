"""
Database connection module for the RAG chatbot.
"""

import os
import logging
from sqlalchemy import create_engine, text # Import text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import OperationalError # Import OperationalError
from app.utils import secrets_manager as sm
# import time # Optional: for a small delay between retries

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global variables for engine and session factory
# These will be initialized and can be re-initialized by _update_db_components
engine = None
SessionLocal = None
DATABASE_URL = None # Will also be updated

# Enable database logging if requested
DATABASE_LOGGING = os.getenv("DATABASE_LOGGING", "false").lower() == "true"
MAX_AUTH_RETRIES = 1 # Max retries specifically for authentication failure

def _update_db_components():
    """
    Helper function to initialize or update DB engine and session factory.
    This function will fetch new credentials and reconfigure the connection.
    """
    global engine, SessionLocal, DATABASE_URL

    logger.info("Attempting to update database components and fetch new credentials...")
    db_creds = sm.get_db_credentials()
    DATABASE_URL = f"mysql+pymysql://{db_creds['user']}:{db_creds['password']}@{db_creds['host']}:{db_creds['port']}/{db_creds['database']}"
    logger.info(f"New DATABASE_URL configured for host: {db_creds['host']}")

    if engine:
        logger.info("Disposing of the old database engine.")
        engine.dispose()

    engine = create_engine(
        DATABASE_URL,
        pool_pre_ping=True,
        pool_recycle=3600,
        echo=DATABASE_LOGGING
    )
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    logger.info("Database engine and SessionLocal factory have been updated/recreated.")

# Initial setup of database components when the module is loaded
try:
    _update_db_components()
except Exception as e:
    logger.critical(f"Failed to initialize database components on module load: {e}", exc_info=True)
    # Depending on desired behavior, you might raise this or handle it
    # For now, critical log and subsequent operations might fail if SessionLocal is None

# Create base class for models
Base = declarative_base()

class DatabaseSession:
    """
    Database session context manager with retry logic for authentication failures.
    """
    def __init__(self):
        self.db: Session | None = None

    def __enter__(self) -> Session:
        global SessionLocal # Ensure we are using the potentially updated SessionLocal

        if not SessionLocal:
            # This can happen if the initial _update_db_components failed
            logger.error("SessionLocal is not initialized. Attempting to re-initialize.")
            try:
                _update_db_components()
            except Exception as e:
                logger.critical(f"Failed to re-initialize SessionLocal in __enter__: {e}", exc_info=True)
                raise Exception("Database session factory (SessionLocal) is not available.") from e
        
        for attempt in range(MAX_AUTH_RETRIES + 1):
            try:
                self.db = SessionLocal()
                # Perform a lightweight query to actually test the connection and credentials
                self.db.execute(text("SELECT 1")) # Use text() for SQL execution
                logger.info(f"Database session acquired successfully (attempt {attempt + 1}).")
                return self.db
            except OperationalError as e:
                logger.warning(f"OperationalError encountered on attempt {attempt + 1}: {e}")
                
                # Check if it's a MySQL authentication error (code 1045 for 'Access denied')
                # This relies on the underlying DBAPI driver (pymysql in this case)
                is_auth_error = False
                if e.orig and hasattr(e.orig, 'args') and len(e.orig.args) > 0:
                    error_code = e.orig.args[0]
                    if error_code == 1045: # MySQL specific error code for access denied
                        is_auth_error = True
                        logger.warning("MySQL authentication error (1045) detected.")
                
                if is_auth_error and attempt < MAX_AUTH_RETRIES:
                    logger.info(f"Attempting to refresh credentials and retry connection (retry {attempt + 1} of {MAX_AUTH_RETRIES}).")
                    try:
                        _update_db_components() # Fetch new creds, re-create engine & SessionLocal
                        # time.sleep(1) # Optional: short delay before retrying
                        continue # Go to the next attempt in the loop
                    except Exception as update_exc:
                        logger.error(f"Failed to update DB components during retry attempt: {update_exc}", exc_info=True)
                        # If updating components fails, re-raise the original OperationalError
                        raise e from update_exc
                else:
                    if is_auth_error:
                        logger.error(f"Authentication error persisted after {MAX_AUTH_RETRIES + 1} attempts.")
                    else:
                        logger.error("OperationalError was not an authentication error or no retries left.")
                    raise e # Re-raise the original error
            except Exception as e: # Catch other unexpected errors during session creation
                logger.error(f"Unexpected error during session creation on attempt {attempt + 1}: {e}", exc_info=True)
                raise e
        
        # Fallback, though loop structure should prevent reaching here if an exception is always raised on failure.
        raise Exception("Failed to acquire database session after all retries.")


    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.db:
            if exc_type is not None:
                # logger.error(f"Error in database session: {str(exc_val)}") # Already logged by OperationalError handler
                if not isinstance(exc_val, OperationalError): # Avoid double logging for handled OperationalErrors
                     logger.error(f"Exception within database session context: {exc_val}", exc_info=True)
                self.db.rollback()
                logger.info("Database session rolled back due to exception.")
            else:
                # Only commit if there was no exception
                # self.db.commit() # Decide if auto-commit on successful exit is desired
                pass
            self.db.close()
            logger.info("Database session closed.")

def get_db():
    """
    Get a database session as a context manager.
    """
    return DatabaseSession()

def init_db():
    """
    Initialize the database by creating all tables.
    """
    global engine # Ensure init_db uses the potentially updated engine
    if not engine:
        logger.error("Database engine not initialized. Cannot run init_db.")
        # Optionally, try to initialize it here if that's desired fallback behavior
        # _update_db_components()
        # if not engine:
        raise Exception("Database engine is not available for init_db.")

    try:
        # Import all models to ensure they are registered with Base
        from .models import User, Group, UserGroup, Document, DocumentAccess, Embedding, Conversation, Message

        # Create tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully using the current engine.")
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}", exc_info=True)
        raise
