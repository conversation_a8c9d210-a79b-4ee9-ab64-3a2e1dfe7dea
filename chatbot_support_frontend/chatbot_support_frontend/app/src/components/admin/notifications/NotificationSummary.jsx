import React, { useState, useMemo } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  FormControl,
  Select,
  MenuItem,
  Pagination,
  LinearProgress
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';

const NotificationSummary = ({ stats, refreshData, isRefreshing }) => {
  // State
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Filter out duplicate notifications
  const uniqueNotifications = useMemo(() => {
    const seen = new Map();
    return stats.notifications.filter(notification => {
      // Create a unique key based on document_id and type
      const key = `${notification.document_id}-${notification.type}`;

      // If we haven't seen this key before, keep the notification
      if (!seen.has(key)) {
        seen.set(key, true);
        return true;
      }

      return false;
    });
  }, [stats.notifications]);

  // Prepare summary data for users
  const prepareSummaryData = () => {
    const userReadCounts = {};

    // Initialize counts for each user
    stats.users.forEach(user => {
      userReadCounts[user.id] = {
        id: user.id,
        username: user.username,
        read: 0,
        unread: 0,
        total: uniqueNotifications.length
      };
    });

    // Count read/unread for each user
    stats.read_status.forEach(status => {
      // Check if this status corresponds to a notification in our unique list
      const notificationExists = uniqueNotifications.some(n => n.id === status.notification_id);

      if (userReadCounts[status.user_id] && notificationExists) {
        userReadCounts[status.user_id].read += 1;
        userReadCounts[status.user_id].unread =
          userReadCounts[status.user_id].total - userReadCounts[status.user_id].read;
      }
    });

    return Object.values(userReadCounts).map(user => ({
      ...user,
      readPercentage: Math.round((user.read / user.total) * 100)
    }));
  };

  const summaryData = prepareSummaryData();

  // Pagination
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(1);
  };

  // Get paginated data
  const paginatedSummaryData = summaryData.slice(
    (page - 1) * rowsPerPage,
    (page - 1) * rowsPerPage + rowsPerPage
  );

  // Get progress color based on percentage
  const getProgressColor = (percentage) => {
    if (percentage >= 75) return 'success';
    if (percentage >= 50) return 'warning';
    return 'error';
  };

  return (
    <Box>
      {/* Header with controls */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Notification Summary
        </Typography>
        <Tooltip title="Refresh data">
          <IconButton
            onClick={refreshData}
            disabled={isRefreshing}
            sx={{
              animation: isRefreshing ? 'spin 1s linear infinite' : 'none',
              '@keyframes spin': {
                '0%': { transform: 'rotate(0deg)' },
                '100%': { transform: 'rotate(360deg)' }
              }
            }}
          >
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Table */}
      <TableContainer component={Paper} sx={{ mb: 3 }}>
        <Table sx={{ minWidth: 650 }} aria-label="notification summary table">
          <TableHead>
            <TableRow>
              <TableCell>User</TableCell>
              <TableCell align="right">Read</TableCell>
              <TableCell align="right">Unread</TableCell>
              <TableCell align="right">Total</TableCell>
              <TableCell>Read %</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedSummaryData.map((user) => (
              <TableRow key={user.id} hover>
                <TableCell component="th" scope="row">
                  {user.username}
                </TableCell>
                <TableCell align="right">{user.read}</TableCell>
                <TableCell align="right">{user.unread}</TableCell>
                <TableCell align="right">{user.total}</TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box sx={{ width: '100%', mr: 1 }}>
                      <LinearProgress
                        variant="determinate"
                        value={user.readPercentage}
                        color={getProgressColor(user.readPercentage)}
                        sx={{ height: 10, borderRadius: 5 }}
                      />
                    </Box>
                    <Box sx={{ minWidth: 35 }}>
                      <Typography variant="body2" color="text.secondary">
                        {`${user.readPercentage}%`}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
            Rows per page:
          </Typography>
          <FormControl variant="standard" size="small">
            <Select
              value={rowsPerPage}
              onChange={handleChangeRowsPerPage}
              displayEmpty
            >
              <MenuItem value={5}>5</MenuItem>
              <MenuItem value={10}>10</MenuItem>
              <MenuItem value={25}>25</MenuItem>
              <MenuItem value={50}>50</MenuItem>
            </Select>
          </FormControl>
        </Box>
        <Pagination
          count={Math.ceil(summaryData.length / rowsPerPage)}
          page={page}
          onChange={handleChangePage}
          color="primary"
        />
      </Box>
    </Box>
  );
};

export default NotificationSummary;
