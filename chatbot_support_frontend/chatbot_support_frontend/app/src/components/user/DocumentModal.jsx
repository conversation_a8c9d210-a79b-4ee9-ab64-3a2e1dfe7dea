import React, { useState, useEffect, useRef } from 'react';
import { XMarkIcon, DocumentArrowUpIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import { useDocument } from '../../contexts/DocumentContext';
import { useUser } from '../../contexts/UserContext';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import ImprovedDropdown from '../common/ImprovedDropdown';
import { dropdownOptionsAPI, API_BASE_URL } from '../../services/api';

const DocumentModal = ({ isOpen, onClose }) => {
  const [documentName, setDocumentName] = useState('');
  const [documentFile, setDocumentFile] = useState(null);
  const [selectedGroups, setSelectedGroups] = useState([]);
  const [serviceName, setServiceName] = useState('');
  const [softwareMenus, setSoftwareMenus] = useState('');
  const [issueType, setIssueType] = useState('');
  const [uploadError, setUploadError] = useState('');
  const [uploading, setUploading] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [documentNumber, setDocumentNumber] = useState('');
  const [showIssueTypeDropdown, setShowIssueTypeDropdown] = useState(false); // New state for conditional rendering

  // State for dropdown options
  const [serviceNameOptions, setServiceNameOptions] = useState([]);
  const [softwareMenuOptions, setSoftwareMenuOptions] = useState([]);
  const [issueTypeOptions, setIssueTypeOptions] = useState([]);
  const [loadingOptions, setLoadingOptions] = useState(false);

  const fileInputRef = useRef(null);
  const { uploadDocument, error: documentError } = useDocument();
  const { groups = [], fetchGroups } = useUser();
  const { user } = useAuth();
  const { darkMode } = useTheme();

  // Fetch dropdown options when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchDropdownOptions();
    }
  }, [isOpen]);

  // Fetch issue type options based on selected software menu
  useEffect(() => {
    const fetchFilteredIssueTypes = async () => {
      if (softwareMenus) {
        setLoadingOptions(true);
        try {
          const selectedSoftwareMenuOption = softwareMenuOptions.find(option => option.value === softwareMenus);
          if (selectedSoftwareMenuOption) {
            const issueTypes = await dropdownOptionsAPI.getIssueTypeOptions(selectedSoftwareMenuOption.id);
            setIssueTypeOptions(issueTypes.map(option => ({
              id: option.id,
              value: option.value,
              label: option.value,
              software_menu_option_id: option.software_menu_option_id
            })));
            setShowIssueTypeDropdown(true); // Show issue type dropdown
          } else {
            // If softwareMenus is set but no matching option found (e.g., after reset), clear issue types
            setIssueTypeOptions([]);
            setShowIssueTypeDropdown(false); // Hide issue type dropdown
          }
        } catch (error) {
          console.error('Error fetching filtered issue type options:', error);
          setIssueTypeOptions([]); // Clear options on error
          setShowIssueTypeDropdown(false); // Hide issue type dropdown
        } finally {
          setLoadingOptions(false);
        }
      } else {
        // If no software menu is selected, clear issue types and hide the dropdown
        setIssueTypeOptions([]);
        setIssueType(''); // Clear selected issue type
        setShowIssueTypeDropdown(false); // Hide issue type dropdown
      }
    };

    fetchFilteredIssueTypes();
  }, [softwareMenus, softwareMenuOptions]); // Depend on softwareMenus and softwareMenuOptions

  // Function to fetch dropdown options
  const fetchDropdownOptions = async () => {
    setLoadingOptions(true);
    try {
      // Fetch service name options
      const serviceNames = await dropdownOptionsAPI.getServiceNameOptions();
      setServiceNameOptions(serviceNames.map(option => ({
        id: option.id,
        value: option.value,
        label: option.value
      })));

      // Fetch software menu options
      const softwareMenus = await dropdownOptionsAPI.getSoftwareMenuOptions();
      setSoftwareMenuOptions(softwareMenus.map(option => ({
        id: option.id,
        value: option.value,
        label: option.value
      })));

      // Fetch issue type options (initially without filter)
      const issueTypes = await dropdownOptionsAPI.getIssueTypeOptions();
      setIssueTypeOptions(issueTypes.map(option => ({
        id: option.id,
        value: option.value,
        label: option.value,
        software_menu_option_id: option.software_menu_option_id
      })));
    } catch (error) {
      console.error('Error fetching dropdown options:', error);
      // Set default options if API fails
      setServiceNameOptions([
        { value: 'Customer Support', label: 'Customer Support' },
        { value: 'Technical Support', label: 'Technical Support' },
        { value: 'Sales', label: 'Sales' },
        { value: 'Billing', label: 'Billing' },
        { value: 'Other', label: 'Other' }
      ]);

      setSoftwareMenuOptions([
        { value: 'Dashboard', label: 'Dashboard' },
        { value: 'Reports', label: 'Reports' },
        { value: 'Settings', label: 'Settings' },
        { value: 'User Management', label: 'User Management' },
        { value: 'Other', label: 'Other' }
      ]);

      setIssueTypeOptions([
        { value: 'Bug', label: 'Bug' },
        { value: 'Feature Request', label: 'Feature Request' },
        { value: 'Question', label: 'Question' },
        { value: 'Documentation', label: 'Documentation' },
        { value: 'Other', label: 'Other' }
      ]);
    } finally {
      setLoadingOptions(false);
    }
  };

  // Functions to add new options
  const handleAddServiceName = async (value) => {
    try {
      // Create the new option in the database
      const newOption = await dropdownOptionsAPI.createServiceNameOption(value);

      // Update the local state with the new option
      const updatedOptions = [...serviceNameOptions, { value: newOption.value, label: newOption.value }];
      setServiceNameOptions(updatedOptions);

      // Return the new option so the dropdown can update
      return newOption;
    } catch (error) {
      console.error('Error adding service name option:', error);
      throw error;
    }
  };

  const handleAddSoftwareMenu = async (value) => {
    try {
      // Create the new option in the database
      const newOption = await dropdownOptionsAPI.createSoftwareMenuOption(value);

      // Update the local state with the new option
      const updatedOptions = [...softwareMenuOptions, { id: newOption.id, value: newOption.value, label: newOption.value }];
      setSoftwareMenuOptions(updatedOptions);

      // Return the new option so the dropdown can update
      return newOption;
    } catch (error) {
      console.error('Error adding software menu option:', error);
      throw error;
    }
  };

  const handleAddIssueType = async (value) => {
    try {
      const selectedSoftwareMenuOption = softwareMenuOptions.find(option => option.value === softwareMenus);
      const softwareMenuId = selectedSoftwareMenuOption ? selectedSoftwareMenuOption.id : null;

      if (!softwareMenuId) {
        throw new Error('Please select a Software Menu before adding a new Issue Type.');
      }

      // Create the new option in the database
      const newOption = await dropdownOptionsAPI.createIssueTypeOption(value, softwareMenuId);

      // Update the local state with the new option
      const updatedOptions = [...issueTypeOptions, { id: newOption.id, value: newOption.value, label: newOption.value, software_menu_option_id: newOption.software_menu_option_id }];
      setIssueTypeOptions(updatedOptions);

      // Return the new option so the dropdown can update
      return newOption;
    } catch (error) {
      console.error('Error adding issue type option:', error);
      throw error;
    }
  };

  // Fetch groups when modal is opened - with debounce to prevent excessive API calls
  useEffect(() => {
    let isMounted = true;
    let fetchTimeout = null;

    if (isOpen) {
      console.log('Modal opened, preparing to fetch groups...');

      // Set user's groups from localStorage first if available
      try {
        const userData = localStorage.getItem('userData');
        if (userData) {
          const parsedUserData = JSON.parse(userData);
          if (parsedUserData.groups && parsedUserData.groups.length > 0) {
            console.log('Setting user groups from localStorage:', parsedUserData.groups);
            setSelectedGroups(parsedUserData.groups);
          }
        }
      } catch (e) {
        console.error('Error getting groups from localStorage:', e);
      }

      // Use a timeout to debounce the API call
      fetchTimeout = setTimeout(() => {
        console.log('Fetching groups after debounce...');
        fetchGroups().then(() => {
          if (!isMounted) return;

          console.log('Groups fetched successfully');

          // If user is not admin, try to get user details directly - but only once
          if (user && user.role !== 'admin' && (!user.groups || user.groups.length === 0)) {
            try {
              // Check if we've already fetched user details recently
              const lastFetchTime = localStorage.getItem('lastUserDetailsFetch');
              const now = Date.now();

              if (!lastFetchTime || (now - parseInt(lastFetchTime)) > 60000) { // Only fetch if it's been more than a minute
                localStorage.setItem('lastUserDetailsFetch', now.toString());

                fetch(`${API_BASE_URL}/users/me/details`, {
                  method: 'GET',
                  headers: {
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json',
                  },
                })
                .then(response => response.json())
                .then(data => {
                  if (!isMounted) return;

                  if (data && data.user && data.user.groups && data.user.groups.length > 0) {
                    console.log('Got user groups directly from /users/me/details:', data.user.groups);
                    setSelectedGroups(data.user.groups);

                    // Update user object in localStorage
                    try {
                      const userData = localStorage.getItem('userData');
                      if (userData) {
                        const parsedUserData = JSON.parse(userData);
                        parsedUserData.groups = data.user.groups;
                        localStorage.setItem('userData', JSON.stringify(parsedUserData));
                      }
                    } catch (e) {
                      console.error('Error updating localStorage:', e);
                    }
                  }
                })
                .catch(error => {
                  console.error('Error fetching user details:', error);
                });
              } else {
                console.log('Skipping user details fetch - fetched recently');
              }
            } catch (error) {
              console.error('Error in direct user details fetch:', error);
            }
          }
        });
      }, 500); // 500ms debounce
    }

    // Cleanup function
    return () => {
      isMounted = false;
      if (fetchTimeout) {
        clearTimeout(fetchTimeout);
      }
    };
  }, [isOpen, fetchGroups, user]);

  // Dark mode is already defined from useTheme()

  // Reset form on close
  useEffect(() => {
    if (!isOpen) {
      setDocumentName('');
      setDocumentFile(null);
      setSelectedGroups([]);
      setServiceName('');
      setSoftwareMenus('');
      setIssueType('');
      setUploadError('');
      setUploadSuccess(false);
      setDocumentNumber('');
      setShowIssueTypeDropdown(false); // Reset visibility on close
    }
  }, [isOpen]);

  // Set user's groups automatically when modal opens
  useEffect(() => {
    if (isOpen && user) {
      if (user.groups && user.groups.length > 0) {
        console.log('Setting user groups in modal:', user.groups);
        setSelectedGroups(user.groups);
      } else {
        // If user has no groups in the context, try to get them from localStorage
        try {
          const userData = localStorage.getItem('userData');
          if (userData) {
            const parsedUserData = JSON.parse(userData);
            if (parsedUserData.groups && Array.isArray(parsedUserData.groups) && parsedUserData.groups.length > 0) {
              console.log('Setting user groups from localStorage:', parsedUserData.groups);
              setSelectedGroups(parsedUserData.groups);
            }
          }
        } catch (error) {
          console.error('Error getting groups from localStorage:', error);
        }
      }
    }
  }, [isOpen, user]);

  // Debug user and groups
  useEffect(() => {
    if (isOpen) {
      console.log('Modal opened with user:', user);
      console.log('Available groups:', groups);
      console.log('Selected groups:', selectedGroups);
    }
  }, [isOpen, user, groups, selectedGroups]);

  // Update upload error if document error changes
  useEffect(() => {
    if (documentError) {
      setUploadError(documentError);
    }
  }, [documentError]);

  // Handle file selection
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setDocumentFile(file);
      // Set document name from file name if not already set
      if (!documentName) {
        setDocumentName(file.name);
      }
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!documentFile) {
      setUploadError('Please select a file to upload');
      return;
    }

    // If no groups are selected, try to use user's groups
    let groupsToUse = selectedGroups;

    // If no groups are selected but we have groups in the context
    if (selectedGroups.length === 0 && user && user.groups && user.groups.length > 0) {
      console.log('Using user groups from context:', user.groups);
      groupsToUse = user.groups;
    }
    // If no groups are selected and no groups in context, try localStorage
    else if (selectedGroups.length === 0) {
      try {
        const userData = localStorage.getItem('userData');
        if (userData) {
          const parsedUserData = JSON.parse(userData);
          if (parsedUserData.groups && Array.isArray(parsedUserData.groups) && parsedUserData.groups.length > 0) {
            console.log('Using user groups from localStorage:', parsedUserData.groups);
            groupsToUse = parsedUserData.groups;
          }
        }
      } catch (error) {
        console.error('Error getting groups from localStorage:', error);
      }
    }

    // If we still have no groups and user is not admin, show error
    if (groupsToUse.length === 0 && (!user || user.role !== 'admin')) {
      // Check if there are any groups available in the system
      if (groups && groups.length > 0) {
        setUploadError('You are not a member of any groups. Please contact your administrator.');
      } else {
        setUploadError('No groups are available in the system. Please contact your administrator.');
      }
      return;
    }

    try {
      setUploading(true);
      setUploadError('');

      // Create form data
      const formData = new FormData();
      formData.append('file', documentFile);

      // Add each group ID as a separate form field entry
      if (groupsToUse && groupsToUse.length > 0) {
        console.log('Adding groups to form data:', groupsToUse);
        // First add the JSON string for backward compatibility
        formData.append('groups', JSON.stringify(groupsToUse));

        // Then add each group ID individually
        groupsToUse.forEach(groupId => {
          formData.append('group_ids', groupId);
        });
      } else {
        console.log('No groups selected for document upload');
        formData.append('groups', '[]');
      }

      // Add the metadata fields
      if (serviceName) {
        formData.append('service_name', serviceName);
      }

      if (softwareMenus) {
        formData.append('software_menus', softwareMenus);
      }

      if (issueType) {
        formData.append('issue_type', issueType);
      }

      // Upload document
      const result = await uploadDocument(formData);

      if (result.success) {
        // If we have a document number, show success message
        if (result.documentNumber) {
          setDocumentNumber(result.documentNumber);
          setUploadSuccess(true);
          // Don't close the modal yet, show the success message with document number
        } else {
          // If no document number, just close the modal
          onClose();
        }
      } else {
        setUploadError(result.error || 'Failed to upload document');
      }
    } catch (error) {
      console.error('Error uploading document:', error);
      setUploadError(error.message || 'An error occurred while uploading the document');
    } finally {
      setUploading(false);
    }
  };

  // If modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 50
    }}>
      <div style={{
        backgroundColor: darkMode ? '#1F2937' : '#FFFFFF',
        borderRadius: '0.5rem',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        width: '90%',
        maxWidth: '500px',
        maxHeight: '90vh',
        overflow: 'auto'
      }}>
        {/* Modal header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '1rem 1.5rem',
          borderBottom: `1px solid ${darkMode ? '#374151' : '#E5E7EB'}`
        }}>
          <h2 style={{
            fontSize: '1.25rem',
            fontWeight: '600',
            color: darkMode ? '#F9FAFB' : '#111827'
          }}>
            Upload Document
          </h2>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              color: darkMode ? '#9CA3AF' : '#6B7280'
            }}
          >
            <XMarkIcon style={{ height: '1.5rem', width: '1.5rem' }} />
          </button>
        </div>

        {/* Modal body */}
        <form onSubmit={handleSubmit}>
          <div style={{ padding: '1.5rem' }}>
            {/* File upload area */}
            <div style={{
              marginBottom: '1.5rem',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '2rem',
              borderRadius: '0.5rem',
              border: `2px dashed ${darkMode ? '#4B5563' : '#D1D5DB'}`,
              backgroundColor: darkMode ? '#374151' : '#F9FAFB',
              cursor: uploadSuccess ? 'default' : 'pointer',
              opacity: uploadSuccess ? 0.7 : 1
            }}
            onClick={() => !uploadSuccess && fileInputRef.current?.click()}
            >
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                style={{ display: 'none' }}
                accept=".pdf,.docx,.doc,.txt"
                disabled={uploadSuccess}
              />

              {documentFile ? (
                <>
                  <DocumentTextIcon style={{ height: '2.5rem', width: '2.5rem', color: darkMode ? '#60A5FA' : '#2563EB', marginBottom: '0.5rem' }} />
                  <p style={{ fontSize: '0.875rem', color: darkMode ? '#D1D5DB' : '#374151', marginBottom: '0.25rem', fontWeight: '500' }}>
                    {documentFile.name}
                  </p>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                    <span style={{
                      fontSize: '0.75rem',
                      color: darkMode ? '#9CA3AF' : '#6B7280',
                      backgroundColor: darkMode ? '#4B5563' : '#E5E7EB',
                      padding: '0.125rem 0.375rem',
                      borderRadius: '0.25rem'
                    }}>
                      {documentFile.name.split('.').pop().toUpperCase()}
                    </span>
                    <span style={{ fontSize: '0.75rem', color: darkMode ? '#9CA3AF' : '#6B7280' }}>
                      {(documentFile.size / 1024 / 1024).toFixed(2)} MB
                    </span>
                  </div>
                  {!uploadSuccess && (
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        setDocumentFile(null);
                        setDocumentName('');
                      }}
                      style={{
                        fontSize: '0.75rem',
                        color: darkMode ? '#60A5FA' : '#2563EB',
                        background: 'none',
                        border: 'none',
                        cursor: 'pointer',
                        padding: '0.25rem 0.5rem',
                        borderRadius: '0.25rem',
                        backgroundColor: darkMode ? '#1E40AF20' : '#DBEAFE'
                      }}
                    >
                      Change File
                    </button>
                  )}
                </>
              ) : (
                <>
                  <DocumentArrowUpIcon style={{ height: '2.5rem', width: '2.5rem', color: darkMode ? '#9CA3AF' : '#6B7280', marginBottom: '0.5rem' }} />
                  <p style={{ fontSize: '0.875rem', color: darkMode ? '#D1D5DB' : '#374151', marginBottom: '0.25rem', fontWeight: '500' }}>
                    Click to upload a document
                  </p>
                  <p style={{ fontSize: '0.75rem', color: darkMode ? '#9CA3AF' : '#6B7280' }}>
                    PDF, DOCX, DOC, TXT up to 10MB
                  </p>
                </>
              )}
            </div>

            {/* Metadata fields - visible for all users */}
            <div style={{ marginBottom: '1.5rem' }}>
              {/* Service Name dropdown */}
              <ImprovedDropdown
                id="serviceName"
                label="Service Name"
                value={serviceName}
                onChange={setServiceName}
                options={serviceNameOptions}
                onAddOption={handleAddServiceName}
                placeholder="Select a service"
                addNewText="+ Add new"
                disabled={loadingOptions}
                maxDisplayItems={5} // Added prop
              />

              {/* Software Menus dropdown */}
              <ImprovedDropdown
                id="softwareMenus"
                label="Software Menus"
                value={softwareMenus}
                onChange={setSoftwareMenus}
                options={softwareMenuOptions}
                onAddOption={handleAddSoftwareMenu}
                placeholder="Select software menu"
                addNewText="+ Add new"
                disabled={loadingOptions}
                maxDisplayItems={5} // Added prop
              />

              {/* Issue Type dropdown - Conditionally rendered */}
              {showIssueTypeDropdown && (
                <ImprovedDropdown
                  id="issueType"
                  label="Issue Type"
                  value={issueType}
                  onChange={setIssueType}
                  options={issueTypeOptions}
                  onAddOption={handleAddIssueType}
                  placeholder="Select issue type"
                  addNewText="+ Add new"
                  disabled={loadingOptions}
                  maxDisplayItems={5} // Added prop
                />
              )}
            </div>

            {/* Group selection - only visible for admins */}
            {user && user.role === 'admin' && (
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: darkMode ? '#D1D5DB' : '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Assign to Groups
                </label>
                <div style={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: '0.5rem',
                  maxHeight: '150px',
                  overflowY: 'auto',
                  padding: '0.5rem',
                  border: `1px solid ${darkMode ? '#4B5563' : '#D1D5DB'}`,
                  borderRadius: '0.375rem'
                }}>
                  {groups && groups.length > 0 ? (
                    groups.map((group) => (
                      <div
                        key={group.id}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          padding: '0.5rem',
                          borderRadius: '0.375rem',
                          backgroundColor: selectedGroups.includes(group.id)
                            ? (darkMode ? '#1E40AF20' : '#DBEAFE')
                            : (darkMode ? '#374151' : '#F3F4F6'),
                          cursor: 'pointer'
                        }}
                        onClick={() => {
                          if (selectedGroups.includes(group.id)) {
                            setSelectedGroups(selectedGroups.filter(id => id !== group.id));
                          } else {
                            setSelectedGroups([...selectedGroups, group.id]);
                          }
                        }}
                      >
                        <input
                          type="checkbox"
                          checked={selectedGroups.includes(group.id)}
                          onChange={() => {}}
                          style={{ cursor: 'pointer' }}
                        />
                        <span style={{
                          fontSize: '0.875rem',
                          color: darkMode ? '#D1D5DB' : '#374151'
                        }}>
                          {group.name}
                        </span>
                      </div>
                    ))
                  ) : (
                    <p style={{ fontSize: '0.875rem', color: darkMode ? '#9CA3AF' : '#6B7280', padding: '0.5rem' }}>
                      No groups available
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* For regular users, show their groups */}
            {user && user.role !== 'admin' && (
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: darkMode ? '#D1D5DB' : '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Your Document Will Be Available To:
                </label>
                {user.groups && user.groups.length > 0 ? (
                  <div style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: '0.5rem',
                    padding: '0.5rem',
                    border: `1px solid ${darkMode ? '#4B5563' : '#D1D5DB'}`,
                    borderRadius: '0.375rem',
                    backgroundColor: darkMode ? '#374151' : '#F9FAFB',
                  }}>
                    {/* If we have group objects with matching IDs, use those */}
                    {groups.length > 0 ? (
                      groups
                        .filter(group => user.groups.includes(group.id))
                        .map((group) => (
                          <div
                            key={group.id}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.5rem',
                              padding: '0.5rem',
                              borderRadius: '0.375rem',
                              backgroundColor: darkMode ? '#1E40AF20' : '#DBEAFE',
                            }}
                          >
                            <span style={{
                              fontSize: '0.875rem',
                              color: darkMode ? '#D1D5DB' : '#374151'
                            }}>
                              {group.name}
                            </span>
                          </div>
                        ))
                    ) : (
                      /* If we only have group IDs but no matching group objects, show the IDs */
                      user.groups.map((groupId) => (
                        <div
                          key={groupId}
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem',
                            padding: '0.5rem',
                            borderRadius: '0.375rem',
                            backgroundColor: darkMode ? '#1E40AF20' : '#DBEAFE',
                          }}
                        >
                          <span style={{
                            fontSize: '0.875rem',
                            color: darkMode ? '#D1D5DB' : '#374151'
                          }}>
                            Group {groupId.substring(0, 8)}...
                          </span>
                        </div>
                      ))
                    )}
                  </div>
                ) : (
                  <div style={{
                    padding: '0.75rem',
                    backgroundColor: darkMode ? 'rgba(248, 113, 113, 0.1)' : '#FEE2E2',
                    color: darkMode ? '#F87171' : '#DC2626',
                    borderRadius: '0.375rem',
                    fontSize: '0.875rem'
                  }}>
                    You are not a member of any groups. Your administrator will need to assign you to a group.
                  </div>
                )}
              </div>
            )}

            {/* Error message */}
            {uploadError && (
              <div style={{
                padding: '0.75rem',
                marginBottom: '1rem',
                backgroundColor: darkMode ? 'rgba(248, 113, 113, 0.1)' : '#FEE2E2',
                color: darkMode ? '#F87171' : '#DC2626',
                borderRadius: '0.375rem',
                fontSize: '0.875rem'
              }}>
                {uploadError}
              </div>
            )}

            {/* Success message with document number */}
            {uploadSuccess && documentNumber && (
              <div style={{
                padding: '0.75rem',
                marginBottom: '1rem',
                backgroundColor: darkMode ? 'rgba(16, 185, 129, 0.1)' : '#D1FAE5',
                color: darkMode ? '#34D399' : '#059669',
                borderRadius: '0.375rem',
                fontSize: '0.875rem',
                display: 'flex',
                flexDirection: 'column',
                gap: '0.5rem'
              }}>
                <div>Document uploaded successfully!</div>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  fontWeight: '600'
                }}>
                  Document Number:
                  <span style={{
                    padding: '0.125rem 0.5rem',
                    backgroundColor: darkMode ? 'rgba(16, 185, 129, 0.2)' : '#A7F3D0',
                    borderRadius: '0.25rem',
                    fontFamily: 'monospace'
                  }}>
                    {documentNumber}
                  </span>
                </div>
              </div>
            )}

            {/* Submit button */}
            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '0.75rem' }}>
              {!uploadSuccess && (
                <button
                  type="button"
                  onClick={onClose}
                  style={{
                    padding: '0.5rem 1rem',
                    borderRadius: '0.375rem',
                    backgroundColor: darkMode ? '#374151' : '#F3F4F6',
                    color: darkMode ? '#D1D5DB' : '#374151',
                    border: 'none',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: 'pointer'
                  }}
                >
                  Cancel
                </button>
              )}
              {uploadSuccess ? (
                <button
                  type="button"
                  onClick={onClose}
                  style={{
                    padding: '0.5rem 1rem',
                    borderRadius: '0.375rem',
                    backgroundColor: darkMode ? '#059669' : '#10B981',
                    color: 'white',
                    border: 'none',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}
                >
                  Close
                </button>
              ) : (
                <button
                  type="submit"
                  disabled={uploading || !documentFile}
                  style={{
                    padding: '0.5rem 1rem',
                    borderRadius: '0.375rem',
                    backgroundColor: (uploading || !documentFile)
                      ? (darkMode ? '#4B5563' : '#D1D5DB')
                      : (darkMode ? '#7C3AED' : '#8B5CF6'),
                    color: 'white',
                    border: 'none',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: (uploading || !documentFile) ? 'not-allowed' : 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}
                >
                  {uploading ? 'Uploading...' : 'Upload Document'}
                </button>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DocumentModal;
