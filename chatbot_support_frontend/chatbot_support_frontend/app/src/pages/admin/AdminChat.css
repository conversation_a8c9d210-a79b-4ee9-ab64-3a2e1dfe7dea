/* Modern Chat Interface Styles */
:root {
  --chat-radius: 1rem;
  --chat-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --chat-transition: all 0.3s ease;
  --user-message-bg: #3B82F6;
  --user-message-color: #FFFFFF;
  --bot-message-bg: #FFFFFF;
  --bot-message-color: #111827;
  --system-message-bg: #F3F4F6;
  --system-message-color: #4B5563;
  --chat-input-bg: #F3F4F6;
  --chat-input-color: #111827;
  --chat-input-placeholder: #9CA3AF;
  --chat-send-button-bg: #3B82F6;
  --chat-send-button-color: #FFFFFF;
  --chat-send-button-disabled-bg: #9CA3AF;
  --chat-header-border: #E5E7EB;
  --chat-timestamp-color: #9CA3AF;
  --chat-typing-dot-color: #9CA3AF;
  --chat-references-bg: #F3F4F6;
  --chat-references-color: #4B5563;
  --chat-references-border: #E5E7EB;
  --chat-references-link: #2563EB;
}

/* Dark mode variables */
.dark {
  --user-message-bg: #2563EB;
  --user-message-color: #FFFFFF;
  --bot-message-bg: #374151;
  --bot-message-color: #F9FAFB;
  --system-message-bg: #4B5563;
  --system-message-color: #F9FAFB;
  --chat-input-bg: #374151;
  --chat-input-color: #F9FAFB;
  --chat-input-placeholder: #9CA3AF;
  --chat-send-button-bg: #2563EB;
  --chat-send-button-color: #FFFFFF;
  --chat-send-button-disabled-bg: #4B5563;
  --chat-header-border: #4B5563;
  --chat-timestamp-color: #9CA3AF;
  --chat-typing-dot-color: #9CA3AF;
  --chat-references-bg: #4B5563;
  --chat-references-color: #F9FAFB;
  --chat-references-border: #6B7280;
  --chat-references-link: #60A5FA;
}

/* Chat container */
.modern-chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
  background-color: var(--bg-primary);
  transition: var(--chat-transition);
}

/* Chat main area */
.modern-chat-main {
  flex: 1;
  display: flex;
  border-radius: var(--chat-radius);
  box-shadow: var(--chat-shadow);
  overflow: hidden;
  height: 100%;
  min-height: 500px; /* Reduced minimum height */
  background-color: var(--bg-secondary);
  transition: var(--chat-transition);
  position: relative; /* Add position relative for z-index context */
  z-index: 1; /* Base z-index */
}

/* Chat content area */
.modern-chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  min-width: 0;
  height: 100%;
  overflow: hidden;
  background-color: var(--bg-primary);
  transition: var(--chat-transition);
  position: relative;
  z-index: 1;
}

/* Chat header */
.modern-chat-header {
  padding: 0.5rem 1rem;
  border-bottom: 1px solid var(--chat-header-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--bg-primary);
  transition: var(--chat-transition);
  flex: 0 0 auto; /* Don't allow flex to grow or shrink */
  z-index: 3;
  height: auto; /* Auto height */
  min-height: 60px; /* Minimum height */
}

.modern-chat-header-left {
  display: flex;
  align-items: center;
}

.modern-sidebar-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: none;
  cursor: pointer;
  margin-right: 0.75rem;
  transition: var(--chat-transition);
}

.modern-sidebar-toggle:hover {
  background-color: var(--hover-color);
  transform: scale(1.05);
}

.modern-ai-avatar {
  height: 2rem;
  width: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--accent-color);
  background: linear-gradient(135deg, #3B82F6, #2563EB);
  margin-right: 0.5rem;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
}

.modern-ai-avatar span {
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
}

.modern-ai-info {
  display: flex;
  flex-direction: column;
}

.modern-ai-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.15rem;
  white-space: nowrap;
}

.modern-ai-status {
  display: flex;
  align-items: center;
}

.modern-status-indicator {
  height: 0.4rem;
  width: 0.4rem;
  border-radius: 50%;
  margin-right: 0.4rem;
  transition: var(--chat-transition);
}

.modern-status-indicator.ready {
  background-color: #10B981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.modern-status-indicator.initializing {
  background-color: #F59E0B;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  animation: pulse 1.5s infinite;
}

.modern-status-text {
  font-size: 0.7rem;
  color: var(--text-secondary);
}

/* Chat action buttons */
.modern-chat-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.modern-action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: var(--chat-transition);
  white-space: nowrap;
}

.modern-action-button:hover {
  background-color: var(--hover-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.modern-action-button:active {
  transform: translateY(0);
}

.modern-action-button svg {
  height: 1rem;
  width: 1rem;
}

/* Chat messages area */
.modern-chat-messages {
  flex: 1 1 auto;
  padding: 0.75rem 1rem;
  overflow-y: auto;
  scrollbar-width: thin;
  background-color: var(--bg-primary);
  background-image:
    radial-gradient(circle at 25px 25px, rgba(59, 130, 246, 0.05) 2px, transparent 0),
    radial-gradient(circle at 75px 75px, rgba(59, 130, 246, 0.05) 2px, transparent 0);
  background-size: 100px 100px;
  transition: var(--chat-transition);
  position: relative;
  z-index: 2;
  min-height: 0; /* Allow container to shrink below content size */
}

.modern-chat-messages::-webkit-scrollbar {
  width: 6px;
}

.modern-chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.modern-chat-messages::-webkit-scrollbar-thumb {
  background-color: var(--text-secondary);
  border-radius: 3px;
  opacity: 0.7;
}

.modern-chat-messages::-webkit-scrollbar-thumb:hover {
  background-color: var(--text-primary);
  opacity: 1;
}

.modern-message-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-height: 100%;
  padding-bottom: 0.5rem;
  position: relative;
  z-index: 2;
}

/* Message styles */
.modern-message-container {
  display: flex;
  max-width: 100%;
  animation: fadeIn 0.3s ease;
}

.modern-message-container.user {
  justify-content: flex-end;
}

.modern-message-container.bot {
  justify-content: flex-start;
}

.modern-message-container.system {
  justify-content: center;
}

.modern-message-bubble {
  max-width: 80%;
  border-radius: 0.75rem;
  padding: 0.75rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  transition: var(--chat-transition);
  position: relative;
}

.modern-message-bubble.user {
  background-color: var(--user-message-bg);
  color: var(--user-message-color);
  border-bottom-right-radius: 0.25rem;
}

.modern-message-bubble.bot {
  background-color: var(--bot-message-bg);
  color: var(--bot-message-color);
  border-bottom-left-radius: 0.25rem;
}

.modern-message-bubble.system {
  background-color: var(--system-message-bg);
  color: var(--system-message-color);
  max-width: 60%;
  text-align: center;
  font-style: italic;
}

.modern-message-content {
  font-size: 0.9375rem;
  line-height: 1.5;
  white-space: pre-wrap;
}

.modern-message-timestamp {
  font-size: 0.75rem;
  margin-top: 0.5rem;
  color: var(--chat-timestamp-color);
  text-align: right;
  opacity: 0.8;
}

/* Document references */
.modern-document-references {
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: var(--chat-references-bg);
  border: 1px solid var(--chat-references-border);
}

.modern-references-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--chat-references-color);
}

.modern-references-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.modern-reference-item {
  font-size: 0.8125rem;
  color: var(--chat-references-color);
}

.modern-reference-link {
  color: var(--chat-references-link);
  text-decoration: none;
  font-weight: 500;
}

.modern-reference-link:hover {
  text-decoration: underline;
}

/* Typing indicator */
.modern-typing-indicator {
  display: flex;
  justify-content: flex-start;
  animation: fadeIn 0.3s ease;
}

.modern-typing-bubble {
  border-radius: 1rem;
  padding: 0.75rem 1rem;
  background-color: var(--bot-message-bg);
  color: var(--bot-message-color);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border-bottom-left-radius: 0.25rem;
}

.modern-typing-dots {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.modern-typing-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: var(--chat-typing-dot-color);
}

.modern-typing-dot:nth-child(1) {
  animation: bounce 1.4s infinite 0.2s;
}

.modern-typing-dot:nth-child(2) {
  animation: bounce 1.4s infinite 0.4s;
}

.modern-typing-dot:nth-child(3) {
  animation: bounce 1.4s infinite 0.6s;
}

/* Error message */
.modern-error-container {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  animation: fadeIn 0.3s ease;
}

.modern-error-message {
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  background-color: rgba(220, 38, 38, 0.1);
  color: #DC2626;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modern-error-icon {
  height: 1rem;
  width: 1rem;
}

/* Chat input area */
.modern-chat-input-container {
  padding: 0.5rem 1rem; /* Reduced padding */
  border-top: 1px solid var(--chat-header-border);
  background-color: var(--bg-primary);
  transition: var(--chat-transition);
  position: relative;
  z-index: 3;
  flex: 0 0 auto; /* Don't allow flex to grow or shrink */
}

.modern-chat-input-form {
  display: flex;
  align-items: flex-end;
  position: relative;
}

.modern-chat-input {
  flex: 1;
  padding: 0.75rem 1rem;
  padding-right: 3rem;
  border-radius: 1.5rem;
  border: 1px solid var(--chat-header-border);
  outline: none;
  font-size: 0.9375rem;
  line-height: 1.5;
  resize: none;
  max-height: 150px;
  min-height: 2.5rem;
  background-color: var(--chat-input-bg);
  color: var(--chat-input-color);
  transition: var(--chat-transition);
}

.modern-chat-input:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.modern-chat-input::placeholder {
  color: var(--chat-input-placeholder);
  text-align: center;
}

.modern-chat-send-button {
  position: absolute;
  right: 0.75rem;
  bottom: 0.5rem;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: var(--chat-send-button-bg);
  color: var(--chat-send-button-color);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--chat-transition);
}

.modern-chat-send-button:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modern-chat-send-button:disabled {
  background-color: var(--chat-send-button-disabled-bg);
  cursor: not-allowed;
}

.modern-send-icon {
  height: 1.25rem;
  width: 1.25rem;
  transform: rotate(45deg); /* Adjust rotation to point up-right */
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* History Dropdown Styles */
.history-dropdown-container {
  position: relative;
}

.history-dropdown-content {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  width: 300px;
  max-height: 400px;
  overflow-y: auto;
  background-color: var(--bg-primary);
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 100;
  border: 1px solid var(--chat-header-border);
  animation: fadeIn 0.2s ease;
  /* Ensure dropdown doesn't affect layout */
  position: absolute;
}

.history-dropdown-content .chat-history {
  width: 100%;
  height: auto;
  max-height: 400px;
  border-right: none;
}

.history-dropdown-content .chat-history-list {
  max-height: 300px;
}

/* Admin specific styles */
.admin-page .modern-chat-container {
  height: calc(100vh - 65px); /* Adjusted height to fit in viewport */
  position: relative;
  z-index: 1;
  max-width: 100%;
  overflow: hidden;
}

.admin-page .modern-chat-main {
  height: 100%;
}

/* Fixed height for chat messages area with scrollbar */
.admin-page .modern-chat-messages {
  flex: 1 1 auto; /* Allow flex to grow and shrink */
  overflow-y: auto;
  min-height: 200px; /* Minimum height */
  max-height: calc(100vh - 180px); /* Maximum height */
}

/* Responsive styles */
@media (max-width: 1200px) {
  .modern-action-button {
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
  }

  .modern-action-button svg {
    height: 0.9rem;
    width: 0.9rem;
  }
}

@media (max-width: 992px) {
  .modern-chat-actions {
    gap: 0.3rem;
  }

  .modern-action-button {
    padding: 0.3rem 0.5rem;
    font-size: 0.75rem;
  }

  .modern-action-button svg {
    height: 0.85rem;
    width: 0.85rem;
  }
}

@media (max-width: 768px) {
  .modern-chat-messages {
    padding: 0.75rem;
  }

  .modern-message-bubble {
    max-width: 85%;
  }

  .modern-chat-header {
    padding: 0.4rem 0.75rem;
  }

  .modern-ai-name {
    font-size: 0.85rem;
  }

  .modern-action-button span {
    display: none;
  }

  .modern-action-button {
    padding: 0.4rem;
  }

  .modern-chat-input-container {
    padding: 0.75rem;
  }
}

@media (max-width: 576px) {
  .modern-chat-actions {
    gap: 0.2rem;
  }

  .modern-action-button {
    padding: 0.3rem;
  }

  .modern-ai-avatar {
    height: 1.75rem;
    width: 1.75rem;
  }
}

@media (max-width: 640px) {
  .modern-chat-actions {
    gap: 0.5rem;
  }

  .modern-action-button {
    padding: 0.5rem;
  }

  .modern-action-button span {
    display: none;
  }

  .history-dropdown-content {
    width: 250px;
    right: -50px;
  }
}

@media (max-width: 480px) {
  .modern-message-bubble {
    max-width: 90%;
  }

  .modern-ai-name {
    font-size: 0.875rem;
  }

  .modern-chat-header {
    padding: 0.5rem 0.75rem;
  }

  .modern-chat-messages {
    padding: 0.5rem 0.75rem;
  }

  .modern-chat-input-container {
    padding: 0.5rem 0.75rem;
  }

  .modern-chat-input {
    padding: 0.5rem 0.75rem;
    padding-right: 2.5rem;
    font-size: 0.875rem;
  }

  .modern-chat-send-button {
    width: 1.75rem;
    height: 1.75rem;
  }

  .modern-send-icon {
    height: 1rem;
    width: 1rem;
  }
}
