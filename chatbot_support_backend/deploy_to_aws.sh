#!/bin/bash

# AWS Docker Deployment Script for Chatbot Backend
# This script helps you deploy your chatbot to AWS with proper Google Cloud authentication

set -e  # Exit on any error

echo "🚀 AWS Docker Deployment Script"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if google_credentials.json exists
if [ ! -f "google_credentials.json" ]; then
    print_error "google_credentials.json not found in current directory"
    echo "Please make sure your Google service account credentials file is in the current directory"
    exit 1
fi

print_status "Found google_credentials.json"

# Encode credentials to base64
print_info "Encoding Google credentials to base64..."
GOOGLE_CREDENTIALS_BASE64=$(base64 -w 0 google_credentials.json)

if [ -z "$GOOGLE_CREDENTIALS_BASE64" ]; then
    print_error "Failed to encode credentials"
    exit 1
fi

print_status "Successfully encoded credentials (${#GOOGLE_CREDENTIALS_BASE64} characters)"

# Set default values
GOOGLE_PROJECT_ID=${GOOGLE_PROJECT_ID:-"support-chat-bot-462614"}
AWS_REGION=${AWS_REGION:-"us-east-1"}
IMAGE_NAME=${IMAGE_NAME:-"chatbot-backend"}
CONTAINER_NAME=${CONTAINER_NAME:-"chatbot-backend"}

# Prompt for required values
echo ""
print_info "Please provide the following information:"

read -p "Docker image name [$IMAGE_NAME]: " input_image
IMAGE_NAME=${input_image:-$IMAGE_NAME}

read -p "Container name [$CONTAINER_NAME]: " input_container
CONTAINER_NAME=${input_container:-$CONTAINER_NAME}

read -p "Google Project ID [$GOOGLE_PROJECT_ID]: " input_project
GOOGLE_PROJECT_ID=${input_project:-$GOOGLE_PROJECT_ID}

read -p "AWS Region [$AWS_REGION]: " input_region
AWS_REGION=${input_region:-$AWS_REGION}

read -p "Database Host: " DB_HOST
read -p "Database User: " DB_USER
read -s -p "Database Password: " DB_PASSWORD
echo ""
read -p "Database Name [internal_apps_db]: " input_db_name
DB_NAME=${input_db_name:-"internal_apps_db"}

read -p "S3 Bucket Name: " S3_BUCKET_NAME
read -p "AWS Access Key ID: " AWS_ACCESS_KEY_ID
read -s -p "AWS Secret Access Key: " AWS_SECRET_ACCESS_KEY
echo ""

# Build Docker image
echo ""
print_info "Building Docker image..."
docker build -t $IMAGE_NAME ./chatbot_support_backend

if [ $? -eq 0 ]; then
    print_status "Docker image built successfully"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Stop and remove existing container if it exists
echo ""
print_info "Stopping existing container (if any)..."
docker stop $CONTAINER_NAME 2>/dev/null || true
docker rm $CONTAINER_NAME 2>/dev/null || true

# Run the container
echo ""
print_info "Starting new container..."

docker run -d \
  --name $CONTAINER_NAME \
  -p 8000:8000 \
  -e ENV=production \
  -e GOOGLE_PROJECT_ID="$GOOGLE_PROJECT_ID" \
  -e GOOGLE_CREDENTIALS_BASE64="$GOOGLE_CREDENTIALS_BASE64" \
  -e DB_HOST="$DB_HOST" \
  -e DB_USER="$DB_USER" \
  -e DB_PASSWORD="$DB_PASSWORD" \
  -e DB_NAME="$DB_NAME" \
  -e S3_BUCKET_NAME="$S3_BUCKET_NAME" \
  -e AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID" \
  -e AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY" \
  -e AWS_REGION="$AWS_REGION" \
  -e USE_REAL_S3=true \
  -e USE_EXTERNAL_CHROMA=false \
  -e VECTOR_STORE_TYPE=chroma \
  -e JWT_SECRET_KEY="$(openssl rand -base64 32)" \
  -e CORS_ORIGINS="*" \
  -e LOG_LEVEL=INFO \
  --restart unless-stopped \
  $IMAGE_NAME

if [ $? -eq 0 ]; then
    print_status "Container started successfully"
else
    print_error "Failed to start container"
    exit 1
fi

# Wait a moment for the container to start
echo ""
print_info "Waiting for container to start..."
sleep 10

# Check container status
echo ""
print_info "Checking container status..."
if docker ps | grep -q $CONTAINER_NAME; then
    print_status "Container is running"
else
    print_error "Container is not running"
    echo "Container logs:"
    docker logs $CONTAINER_NAME
    exit 1
fi

# Check application logs
echo ""
print_info "Checking application logs for Google credentials setup..."
docker logs $CONTAINER_NAME 2>&1 | grep -i "google\|credential" | tail -10

# Test health endpoint
echo ""
print_info "Testing health endpoint..."
sleep 5
if curl -s http://localhost:8000/health > /dev/null; then
    print_status "Health endpoint is responding"
else
    print_warning "Health endpoint not responding yet (this is normal, may take a few more seconds)"
fi

# Final instructions
echo ""
echo "🎉 Deployment completed!"
echo "======================="
print_status "Container: $CONTAINER_NAME"
print_status "Image: $IMAGE_NAME"
print_status "Port: 8000"
print_status "Health check: http://localhost:8000/health"
print_status "API docs: http://localhost:8000/docs"

echo ""
print_info "To check logs: docker logs $CONTAINER_NAME"
print_info "To stop: docker stop $CONTAINER_NAME"
print_info "To restart: docker restart $CONTAINER_NAME"

echo ""
print_warning "Look for this message in the logs to confirm Google credentials are working:"
echo "   'Google Cloud credentials configured: /tmp/google_credentials_xyz.json'"

echo ""
print_info "If you see authentication errors, check the logs and verify your credentials"
