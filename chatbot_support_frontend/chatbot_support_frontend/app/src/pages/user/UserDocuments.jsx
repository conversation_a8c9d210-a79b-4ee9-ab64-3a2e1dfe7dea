import React, { useState, useEffect } from 'react';
import {
  DocumentTextIcon,
  DocumentPlusIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  MagnifyingGlassIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { useDocument } from '../../contexts/DocumentContext';
import { useUser } from '../../contexts/UserContext';
import DocumentModal from '../../components/user/DocumentModal';
import { API_BASE_URL } from '../../config/apiConfig';

const UserDocuments = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [showDocumentModal, setShowDocumentModal] = useState(false);

  // Pagination state for documents
  const [currentDocPage, setCurrentDocPage] = useState(1);
  const documentsPerPage = 5; // Show 5 documents per page

  // Get dark mode from context or props
  const darkMode = document.documentElement.classList.contains('dark');

  // Get documents from context
  const {
    documents: apiDocuments,
    fetchDocuments,
    uploadDocument,
    deleteDocument,
    getDocumentUrl,
    loading: documentLoading,
    error: documentError
  } = useDocument();

  // Fetch documents on component mount
  useEffect(() => {
    const loadDocuments = async () => {
      try {
        setLoading(true);
        await fetchDocuments();
      } catch (error) {
        console.error('Error fetching documents:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDocuments();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Function to handle exporting all documents
  const handleExportAllDocuments = async () => {
    try {
      setExportLoading(true);

      // Get auth token from local storage
      const authToken - localStorage.getItem('authToken');

      // call the export api endpoint
      const response = await fetch(`${API_BASE_URL}/documents/export-all`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to export documents');
      }

      const data = await response.json();

      if (data.url) {
        // Open the download URL in a new tab
        window.open(data.url, '_blank');
      } else {
        throw new Error('No download URL provided.');
      }
    } catch (error) {
      console.error('Error exporting documents: ', error);
      alert(`Error exporting documents: ${error.message || 'An unexpected error occurred'}`);
    } finally {
      setExportLoading(false);
    }
  }

  // Filter documents based on search term
  const filteredDocuments = apiDocuments?.filter(doc =>
    doc.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Calculate total pages for documents pagination
  const totalDocPages = Math.ceil(filteredDocuments.length / documentsPerPage);

  // Get current page documents
  const indexOfLastDoc = currentDocPage * documentsPerPage;
  const indexOfFirstDoc = indexOfLastDoc - documentsPerPage;
  const currentPageDocuments = filteredDocuments.slice(indexOfFirstDoc, indexOfLastDoc);

  // Handle page changes
  const handlePreviousDocPage = () => {
    setCurrentDocPage(prev => Math.max(prev - 1, 1));
  };

  const handleNextDocPage = () => {
    setCurrentDocPage(prev => Math.min(prev + 1, totalDocPages));
  };

  return (
    <div className="user-documents-container" style={{
        height: 'calc(100vh - 65px)',
        display: 'flex',
        flexDirection: 'column'
      }}>
      <div className="user-documents-header" style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '1rem',
        marginBottom: '1rem'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: '1rem'
        }}>
          <h2 style={{
            fontSize: '1.5rem',
            fontWeight: '600',
            color: darkMode ? '#F9FAFB' : '#111827'
          }}>
            My Documents
          </h2>

          <div style={{
            display: 'flex',
            gap: '0.75rem',
            alignItems: 'center'
          }}>
            {/* Export all documents button */}
            <button
              onClick={handleExportAllDocuments}
              disabled={exportLoading}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                backgroundColor: darkMode ? '#7C3AED' : '#8B5CF6',
                color: darkMode ? 'white' : '#111827',
                padding: '0.5rem 1rem',
                borderRadius: '0.375rem',
                cursor: exportLoading ? 'not-allowed' : 'pointer',
                border: 'none',
                whiteSpace: 'nowrap',
                opacity: exportLoading ? 0.7 : 1
              }}
            >
              <ArrowDownTrayIcon style={{ height: '1.25rem', width: '1.25rem' }} />
              {exportLoading ? 'Exporting...' : 'Export All Documents'}
            </button>
          </div>

          <button
            onClick={() => setShowDocumentModal(true)}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: darkMode ? '#7C3AED' : '#8B5CF6',
              color: 'white',
              padding: '0.5rem 1rem',
              borderRadius: '0.375rem',
              cursor: 'pointer',
              border: 'none',
              whiteSpace: 'nowrap'
            }}
          >
            <DocumentPlusIcon style={{ height: '1.25rem', width: '1.25rem' }} />
            Upload Document
          </button>
        </div>

        {/* Search bar in its own row */}
        <div style={{
          position: 'relative',
          maxWidth: '100%',
          width: '100%',
          marginBottom: '0.5rem'
        }}>
          <div style={{
            position: 'relative',
            width: '100%',
            maxWidth: '500px'
          }}>
            <input
              type="text"
              placeholder="Search documents..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                // Reset to first page when searching
                setCurrentDocPage(1);
              }}
              style={{
                width: '100%',
                padding: '0.75rem 1rem 0.75rem 2.75rem',
                borderRadius: '0.5rem',
                border: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`,
                backgroundColor: darkMode ? '#374151' : '#F9FAFB',
                color: darkMode ? '#F9FAFB' : '#111827',
                fontSize: '0.875rem',
                boxSizing: 'border-box',
                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
              }}
            />
            <MagnifyingGlassIcon style={{
              position: 'absolute',
              left: '1rem',
              top: '50%',
              transform: 'translateY(-50%)',
              height: '1.25rem',
              width: '1.25rem',
              color: darkMode ? '#9CA3AF' : '#6B7280'
            }} />
          </div>
        </div>
      </div>

      {/* Document list */}
      <div style={{
        overflowX: 'auto',
        flex: '1 1 auto',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {loading || documentLoading ? (
          <div style={{
            padding: '2rem',
            textAlign: 'center',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '1rem'
          }}>
            <div style={{
              width: '2rem',
              height: '2rem',
              border: `2px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`,
              borderTopColor: darkMode ? '#60A5FA' : '#3B82F6',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }} />
            <p style={{ color: darkMode ? '#D1D5DB' : '#6B7280' }}>Loading documents...</p>
            <style>
              {`
                @keyframes spin {
                  to { transform: rotate(360deg); }
                }
              `}
            </style>
          </div>
        ) : documentError ? (
          <div style={{
            padding: '2rem',
            textAlign: 'center',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '1rem',
            color: darkMode ? '#F87171' : '#DC2626'
          }}>
            <p>Error loading documents: {documentError}</p>
            <button
              onClick={() => fetchDocuments()}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: darkMode ? '#374151' : '#F3F4F6',
                color: darkMode ? '#F9FAFB' : '#111827',
                border: 'none',
                borderRadius: '0.375rem',
                cursor: 'pointer',
                fontSize: '0.875rem',
                fontWeight: '500'
              }}
            >
              Try Again
            </button>
          </div>
        ) : filteredDocuments.length > 0 ? (
          <table style={{
            width: '100%',
            minWidth: '800px',
            borderCollapse: 'separate',
            borderSpacing: 0,
            tableLayout: 'fixed'
          }}>
            <thead style={{
              backgroundColor: darkMode ? '#4B5563' : '#F9FAFB',
              borderBottom: `1px solid ${darkMode ? '#6B7280' : '#E5E7EB'}`
            }}>
              <tr>
                <th style={{
                  padding: '0.75rem 1.5rem',
                  textAlign: 'left',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  color: darkMode ? '#D1D5DB' : '#6B7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em',
                  width: '10%'
                }}>
                  Document #
                </th>
                <th style={{
                  padding: '0.75rem 1.5rem',
                  textAlign: 'left',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  color: darkMode ? '#D1D5DB' : '#6B7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em',
                  width: '15%'
                }}>
                  Name
                </th>
                <th style={{
                  padding: '0.75rem 1.5rem',
                  textAlign: 'left',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  color: darkMode ? '#D1D5DB' : '#6B7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em',
                  width: '8%'
                }}>
                  Service
                </th>
                <th style={{
                  padding: '0.75rem 1.5rem',
                  textAlign: 'left',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  color: darkMode ? '#D1D5DB' : '#6B7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em',
                  width: '8%'
                }}>
                  Software
                </th>
                <th style={{
                  padding: '0.75rem 1.5rem',
                  textAlign: 'left',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  color: darkMode ? '#D1D5DB' : '#6B7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em',
                  width: '8%'
                }}>
                  Issue
                </th>
                <th style={{
                  padding: '0.75rem 1.5rem',
                  textAlign: 'left',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  color: darkMode ? '#D1D5DB' : '#6B7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em',
                  width: '8%'
                }}>
                  Type
                </th>
                <th style={{
                  padding: '0.75rem 1.5rem',
                  textAlign: 'left',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  color: darkMode ? '#D1D5DB' : '#6B7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em',
                  width: '10%'
                }}>
                  Groups
                </th>
                <th style={{
                  padding: '0.75rem 1.5rem',
                  textAlign: 'left',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  color: darkMode ? '#D1D5DB' : '#6B7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em',
                  width: '10%'
                }}>
                  Uploaded By
                </th>
                <th style={{
                  padding: '0.75rem 1.5rem',
                  textAlign: 'left',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  color: darkMode ? '#D1D5DB' : '#6B7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em',
                  width: '10%'
                }}>
                  Upload Date
                </th>
                <th style={{
                  padding: '0.75rem 1.5rem',
                  textAlign: 'right',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  color: darkMode ? '#D1D5DB' : '#6B7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em',
                  width: '8%'
                }}>
                  Actions
                </th>
              </tr>
            </thead>
            <tbody style={{
              backgroundColor: darkMode ? '#374151' : '#FFFFFF',
              borderTop: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`
            }}>
              {currentPageDocuments.map((document) => (
                <tr key={document.id} style={{
                  borderBottom: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`
                }}>
                  <td style={{
                    padding: '1rem 1.5rem',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: darkMode ? '#F9FAFB' : '#111827',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      height: '2rem'
                    }}>
                      <span style={{
                        padding: '0.125rem 0.5rem',
                        display: 'inline-flex',
                        fontSize: '0.875rem',
                        lineHeight: '1.25rem',
                        fontWeight: '600',
                        borderRadius: '0.375rem',
                        backgroundColor: darkMode ? '#4B5563' : '#F3F4F6',
                        color: darkMode ? '#E5E7EB' : '#374151'
                      }}>
                        {document.document_number || 'N/A'}
                      </span>
                    </div>
                  </td>
                  <td style={{
                    padding: '1rem 1.5rem',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: darkMode ? '#F9FAFB' : '#111827',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      gap: '0.5rem',
                      overflow: 'hidden',
                      padding: '0.5rem 0'
                    }}>
                      <div style={{
                        minWidth: '2rem',
                        height: '2rem',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: '0.375rem',
                        backgroundColor: document.type === 'PDF' ? (darkMode ? '#DC262630' : '#FEE2E2') :
                                      document.type === 'DOCX' ? (darkMode ? '#2563EB30' : '#DBEAFE') :
                                      document.type === 'XLSX' ? (darkMode ? '#05966930' : '#D1FAE5') :
                                      (darkMode ? '#7C3AED30' : '#EDE9FE'),
                        flexShrink: 0,
                        marginTop: '0.125rem'
                      }}>
                        <DocumentTextIcon style={{
                          height: '1.25rem',
                          width: '1.25rem',
                          color: document.type === 'PDF' ? (darkMode ? '#FCA5A5' : '#DC2626') :
                                document.type === 'DOCX' ? (darkMode ? '#93C5FD' : '#2563EB') :
                                document.type === 'XLSX' ? (darkMode ? '#A7F3D0' : '#059669') :
                                (darkMode ? '#C4B5FD' : '#7C3AED')
                        }} />
                      </div>
                      <span style={{
                        whiteSpace: 'normal',
                        wordBreak: 'break-word',
                        lineHeight: '1.25rem'
                      }}>
                        {document.name}
                      </span>
                    </div>
                  </td>
                  <td style={{
                    padding: '1rem 1.5rem',
                    whiteSpace: 'nowrap',
                    fontSize: '0.875rem',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      height: '2rem'
                    }}>
                      {document.service_name || '-'}
                    </div>
                  </td>
                  <td style={{
                    padding: '1rem 1.5rem',
                    whiteSpace: 'nowrap',
                    fontSize: '0.875rem',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      height: '2rem'
                    }}>
                      {document.software_menus || '-'}
                    </div>
                  </td>
                  <td style={{
                    padding: '1rem 1.5rem',
                    whiteSpace: 'nowrap',
                    fontSize: '0.875rem',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      height: '2rem'
                    }}>
                      {document.issue_type || '-'}
                    </div>
                  </td>
                  <td style={{
                    padding: '1rem 1.5rem',
                    whiteSpace: 'nowrap',
                    fontSize: '0.875rem',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      height: '2rem'
                    }}>
                      {document.type}
                    </div>
                  </td>
                  <td style={{
                    padding: '1rem 1.5rem',
                    fontSize: '0.875rem',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}>
                    <div style={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      gap: '0.25rem',
                      minHeight: '2rem',
                      alignItems: 'center'
                    }}>
                      {document.groups && Array.isArray(document.groups) ? (
                        document.groups.map((group, index) => (
                          <span key={index} style={{
                            padding: '0.125rem 0.5rem',
                            display: 'inline-flex',
                            fontSize: '0.75rem',
                            lineHeight: '1.25rem',
                            fontWeight: '600',
                            borderRadius: '9999px',
                            backgroundColor: darkMode ? '#1d4ed830' : '#dbeafe',
                            color: darkMode ? '#bfdbfe' : '#1d4ed8'
                          }}>
                            {group}
                          </span>
                        ))
                      ) : (
                        <span style={{
                          padding: '0.125rem 0.5rem',
                          display: 'inline-flex',
                          fontSize: '0.75rem',
                          lineHeight: '1.25rem',
                          fontWeight: '600',
                          borderRadius: '9999px',
                          backgroundColor: darkMode ? '#4b546330' : '#f3f4f6',
                          color: darkMode ? '#9ca3af' : '#6b7280'
                        }}>
                          No groups
                        </span>
                      )}
                    </div>
                  </td>
                  <td style={{
                    padding: '1rem 1.5rem',
                    fontSize: '0.875rem',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      height: '2rem'
                    }}>
                      <span style={{
                        padding: '0.125rem 0.5rem',
                        display: 'inline-flex',
                        fontSize: '0.75rem',
                        lineHeight: '1.25rem',
                        fontWeight: '600',
                        borderRadius: '9999px',
                        backgroundColor: darkMode ? '#7e22ce30' : '#f3e8ff',
                        color: darkMode ? '#e9d5ff' : '#7e22ce',
                        maxWidth: '100%',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                      }}>
                        {document.uploaderName || 'Unknown'}
                      </span>
                    </div>
                  </td>
                  <td style={{
                    padding: '1rem 1.5rem',
                    fontSize: '0.875rem',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      height: '2rem'
                    }}>
                      {document.uploadDate}
                    </div>
                  </td>
                  <td style={{
                    padding: '1rem 1.5rem',
                    textAlign: 'right',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'flex-end',
                      height: '2rem'
                    }}>
                      <button
                        onClick={async () => {
                          try {
                            setLoading(true);
                            const result = await getDocumentUrl(document.id);
                            if (!result.success) {
                              alert(`Failed to get document URL: ${result.error || 'Unknown error'}`);
                            } else {
                              // Open the document in a new tab
                              window.open(result.url, '_blank');
                            }
                          } catch (error) {
                            console.error('Error getting document URL:', error);
                            alert(`Error getting document URL: ${error.message || error}`);
                          } finally {
                            setLoading(false);
                          }
                        }}
                        style={{
                          background: 'none',
                          border: 'none',
                          cursor: 'pointer',
                          color: darkMode ? '#60A5FA' : '#2563EB',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: '2rem',
                          height: '2rem',
                          borderRadius: '0.375rem',
                          transition: 'background-color 0.2s ease'
                        }}
                        title="View Document"
                        onMouseOver={(e) => e.currentTarget.style.backgroundColor = darkMode ? 'rgba(96, 165, 250, 0.1)' : 'rgba(37, 99, 235, 0.05)'}
                        onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                      >
                        <EyeIcon style={{ height: '1.25rem', width: '1.25rem' }} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <div style={{ padding: '1rem', textAlign: 'center' }}>
            <p style={{ color: darkMode ? '#D1D5DB' : '#6B7280' }}>No documents found</p>
          </div>
        )}
      </div>

      {/* Pagination Controls */}
      {filteredDocuments.length > documentsPerPage && (
        <div style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '1rem 1.5rem',
          borderTop: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`,
          flexWrap: 'wrap',
          gap: '1rem',
          marginTop: 'auto'
        }}>
          <div style={{
            fontSize: '0.875rem',
            color: darkMode ? '#D1D5DB' : '#6B7280',
            whiteSpace: 'nowrap'
          }}>
            Showing {indexOfFirstDoc + 1}-{Math.min(indexOfLastDoc, filteredDocuments.length)} of {filteredDocuments.length} documents
          </div>
          <div style={{
            display: 'flex',
            gap: '0.5rem',
            marginLeft: 'auto'
          }}>
            <button
              onClick={handlePreviousDocPage}
              disabled={currentDocPage === 1}
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '0.5rem',
                borderRadius: '0.375rem',
                backgroundColor: currentDocPage === 1 ? (darkMode ? '#374151' : '#F3F4F6') : (darkMode ? '#4B5563' : '#FFFFFF'),
                color: currentDocPage === 1 ? (darkMode ? '#6B7280' : '#9CA3AF') : (darkMode ? '#D1D5DB' : '#374151'),
                border: `1px solid ${darkMode ? '#4B5563' : '#D1D5DB'}`,
                cursor: currentDocPage === 1 ? 'not-allowed' : 'pointer'
              }}
              aria-label="Previous page"
            >
              <ChevronLeftIcon style={{ height: '1.25rem', width: '1.25rem' }} />
            </button>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.25rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: darkMode ? '#D1D5DB' : '#374151',
              minWidth: '100px',
              justifyContent: 'center'
            }}>
              <span>Page {currentDocPage} of {totalDocPages}</span>
            </div>

            <button
              onClick={handleNextDocPage}
              disabled={currentDocPage === totalDocPages}
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '0.5rem',
                borderRadius: '0.375rem',
                backgroundColor: currentDocPage === totalDocPages ? (darkMode ? '#374151' : '#F3F4F6') : (darkMode ? '#4B5563' : '#FFFFFF'),
                color: currentDocPage === totalDocPages ? (darkMode ? '#6B7280' : '#9CA3AF') : (darkMode ? '#D1D5DB' : '#374151'),
                border: `1px solid ${darkMode ? '#4B5563' : '#D1D5DB'}`,
                cursor: currentDocPage === totalDocPages ? 'not-allowed' : 'pointer'
              }}
              aria-label="Next page"
            >
              <ChevronRightIcon style={{ height: '1.25rem', width: '1.25rem' }} />
            </button>
          </div>
        </div>
      )}

      {/* Document Upload Modal */}
      <DocumentModal
        isOpen={showDocumentModal}
        onClose={() => setShowDocumentModal(false)}
      />
    </div>
  );
};

export default UserDocuments;
