import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  Typography,
  Chip,
  IconButton,
  Collapse,
  Divider
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import DescriptionIcon from '@mui/icons-material/Description';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import NotificationsIcon from '@mui/icons-material/Notifications';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';

const NotificationCard = ({ notification, documentName }) => {
  const [expanded, setExpanded] = useState(false);

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get icon based on notification type
  const getTypeIcon = () => {
    switch (notification.type) {
      case 'document_added':
        return <DescriptionIcon sx={{ color: '#4caf50' }} />;
      case 'document_deleted':
        return <DeleteIcon sx={{ color: '#f44336' }} />;
      case 'document_updated':
        return <EditIcon sx={{ color: '#ff9800' }} />;
      default:
        return <NotificationsIcon sx={{ color: '#2196f3' }} />;
    }
  };

  // Get color based on notification type
  const getTypeColor = () => {
    switch (notification.type) {
      case 'document_added':
        return 'success';
      case 'document_deleted':
        return 'error';
      case 'document_updated':
        return 'warning';
      default:
        return 'primary';
    }
  };

  // Format notification type for display
  const formatType = (type) => {
    if (!type) return 'Unknown';
    return type
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <Card 
      sx={{ 
        transition: 'transform 0.2s, box-shadow 0.2s',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: 3
        }
      }}
    >
      <CardContent sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
          <Box 
            sx={{ 
              p: 1, 
              borderRadius: '50%', 
              bgcolor: `${getTypeColor()}.light`,
              mr: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            {getTypeIcon()}
          </Box>
          <Box sx={{ flexGrow: 1 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="subtitle1" component="div">
                {notification.title}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {notification.is_read ? (
                  <CheckCircleIcon color="success" fontSize="small" sx={{ mr: 0.5 }} />
                ) : (
                  <CancelIcon color="error" fontSize="small" sx={{ mr: 0.5 }} />
                )}
                <IconButton 
                  size="small" 
                  onClick={handleExpandClick}
                  aria-expanded={expanded}
                  aria-label="show more"
                >
                  {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', alignItems: 'center', mt: 0.5, gap: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary', fontSize: 16 }} />
                <Typography variant="body2" color="text.secondary">
                  {formatDate(notification.created_at)}
                </Typography>
              </Box>
              {documentName && (
                <Chip 
                  icon={<DescriptionIcon />} 
                  label={documentName} 
                  size="small" 
                  variant="outlined" 
                />
              )}
            </Box>
          </Box>
        </Box>
        
        <Collapse in={expanded} timeout="auto" unmountOnExit>
          <Box sx={{ mt: 2 }}>
            <Divider />
            <Typography variant="body2" sx={{ mt: 2 }}>
              {notification.message || 'No additional details available.'}
            </Typography>
            <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
              <Chip 
                label={formatType(notification.type)} 
                size="small" 
                color={getTypeColor()} 
              />
            </Box>
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  );
};

export default NotificationCard;
