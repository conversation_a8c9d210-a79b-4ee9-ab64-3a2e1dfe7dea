import React, { createContext, useState, useEffect, useContext } from 'react';

// Create the theme context
export const ThemeContext = createContext();

// Custom hook to use the theme context
export const useTheme = () => useContext(ThemeContext);

// Theme provider component
export const ThemeProvider = ({ children }) => {
  // Initialize dark mode state from localStorage or system preference
  const [darkMode, setDarkMode] = useState(() => {
    // Check for saved preference first
    const savedMode = localStorage.getItem('darkMode');
    if (savedMode !== null) {
      return savedMode === 'true';
    }

    // Fall back to system preference
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return true;
    }

    return false;
  });

  // Apply dark mode class to body when component mounts or darkMode changes
  useEffect(() => {
    console.log('ThemeContext - Applying theme:', darkMode ? 'dark' : 'light');

    if (darkMode) {
      document.documentElement.classList.add('dark');
      document.body.classList.add('dark');
      // Add force-dark-mode class for testing
      document.body.classList.add('force-dark-mode');
      document.body.style.backgroundColor = '#1F2937';
      document.body.style.color = '#F9FAFB';
    } else {
      document.documentElement.classList.remove('dark');
      document.body.classList.remove('dark');
      // Remove force-dark-mode class
      document.body.classList.remove('force-dark-mode');
      document.body.style.backgroundColor = '#FFFFFF';
      document.body.style.color = '#111827';
    }

    // Force a repaint to ensure styles are applied
    document.body.style.display = 'none';
    const reflow = document.body.offsetHeight; // Trigger a reflow
    document.body.style.display = '';
    console.log('Forced reflow:', reflow);

    // Save preference to localStorage
    localStorage.setItem('darkMode', darkMode.toString());

    console.log('ThemeContext - Theme applied, body classes:', document.body.classList.toString());
  }, [darkMode]);

  // Toggle dark mode
  const toggleDarkMode = () => {
    console.log('ThemeContext - toggleDarkMode called, current value:', darkMode);
    setDarkMode(prevMode => {
      console.log('ThemeContext - Setting darkMode from', prevMode, 'to', !prevMode);
      return !prevMode;
    });
  };

  // Listen for system preference changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleChange = (e) => {
      // Only update if user hasn't set a preference
      if (localStorage.getItem('darkMode') === null) {
        setDarkMode(e.matches);
      }
    };

    // Add event listener
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
    } else {
      // Fallback for older browsers
      mediaQuery.addListener(handleChange);
    }

    // Clean up
    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', handleChange);
      } else {
        // Fallback for older browsers
        mediaQuery.removeListener(handleChange);
      }
    };
  }, []);

  // Theme object with colors for different UI elements
  const theme = {
    colors: darkMode ? {
      // Dark theme colors
      background: {
        primary: '#1F2937',
        secondary: '#374151',
        tertiary: '#4B5563',
      },
      text: {
        primary: '#F9FAFB',
        secondary: '#D1D5DB',
        tertiary: '#9CA3AF',
      },
      border: {
        primary: '#4B5563',
        secondary: '#6B7280',
      },
      accent: {
        primary: '#3B82F6',
        secondary: '#60A5FA',
      },
      success: {
        primary: '#059669',
        light: '#A7F3D0',
        background: '#05966930',
      },
      error: {
        primary: '#DC2626',
        light: '#FCA5A5',
        background: '#DC262630',
      },
      warning: {
        primary: '#D97706',
        light: '#FCD34D',
        background: '#********',
      },
      info: {
        primary: '#2563EB',
        light: '#BFDBFE',
        background: '#2563EB30',
      },
      admin: {
        primary: '#7E22CE',
        light: '#E9D5FF',
        background: '#7E22CE30',
      },
      user: {
        primary: '#1D4ED8',
        light: '#BFDBFE',
        background: '#1D4ED830',
      },
    } : {
      // Light theme colors
      background: {
        primary: '#FFFFFF',
        secondary: '#F9FAFB',
        tertiary: '#F3F4F6',
      },
      text: {
        primary: '#111827',
        secondary: '#6B7280',
        tertiary: '#9CA3AF',
      },
      border: {
        primary: '#E5E7EB',
        secondary: '#D1D5DB',
      },
      accent: {
        primary: '#2563EB',
        secondary: '#3B82F6',
      },
      success: {
        primary: '#059669',
        light: '#D1FAE5',
        background: '#D1FAE5',
      },
      error: {
        primary: '#DC2626',
        light: '#FEE2E2',
        background: '#FEE2E2',
      },
      warning: {
        primary: '#D97706',
        light: '#FEF3C7',
        background: '#FEF3C7',
      },
      info: {
        primary: '#2563EB',
        light: '#DBEAFE',
        background: '#DBEAFE',
      },
      admin: {
        primary: '#7E22CE',
        light: '#F3E8FF',
        background: '#F3E8FF',
      },
      user: {
        primary: '#1D4ED8',
        light: '#DBEAFE',
        background: '#DBEAFE',
      },
    },
    darkMode,
    toggleDarkMode,
  };

  return (
    <ThemeContext.Provider value={theme}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeProvider;
