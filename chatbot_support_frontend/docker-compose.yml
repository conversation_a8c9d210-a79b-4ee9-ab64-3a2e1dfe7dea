version: '3.8'

services:
  frontend:
    build:
      context: ./chatbot_support_frontend
      dockerfile: Dockerfile
    container_name: chatbot-frontend
    ports:
      - "3001:80"
    environment:
      - REACT_APP_API_URL=${REACT_APP_API_URL}
      - REACT_APP_ENV=${REACT_APP_ENV}
      - REACT_APP_VERSION=${REACT_APP_VERSION}
    networks:
      - chatbot-network
    restart: unless-stopped

networks:
  chatbot-network:
    name: chatbot_support_backend_chatbot-network
    external: false