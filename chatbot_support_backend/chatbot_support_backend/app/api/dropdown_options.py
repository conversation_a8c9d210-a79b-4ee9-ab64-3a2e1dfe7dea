"""
API endpoints for managing dropdown options.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
import uuid

from app.database.db import get_db
from app.database.models import ServiceNameOption, SoftwareMenuOption, IssueTypeOption
# Import get_current_user from auth.py
from app.api.auth import get_current_user

router = APIRouter()

# Pydantic models for request/response
class DropdownOptionCreate(BaseModel):
    value: str

class DropdownOption(BaseModel):
    id: str
    value: str
    created_at: Optional[str] = None
    created_by: Optional[str] = None

class IssueTypeDropdownOptionCreate(BaseModel):
    value: str
    software_menu_option_id: str

class IssueTypeDropdownOption(BaseModel):
    id: str
    value: str
    software_menu_option_id: Optional[str] = None
    created_at: Optional[str] = None
    created_by: Optional[str] = None


# Service Name Options
@router.get("/service-names", response_model=List[DropdownOption], tags=["Dropdown Options"])
async def get_service_name_options(current_user: dict = Depends(get_current_user)):
    """Get all service name options."""
    with get_db() as db:
        options = db.query(ServiceNameOption).all()
        return [
            DropdownOption(
                id=option.id,
                value=option.value,
                created_at=option.created_at.isoformat() if option.created_at else None,
                created_by=option.created_by
            ) for option in options
        ]

@router.post("/service-names", response_model=DropdownOption, tags=["Dropdown Options"])
async def create_service_name_option(option: DropdownOptionCreate, current_user: dict = Depends(get_current_user)):
    """Create a new service name option."""
    with get_db() as db:
        # Check if option already exists
        existing = db.query(ServiceNameOption).filter(ServiceNameOption.value == option.value).first()
        if existing:
            return DropdownOption(
                id=existing.id,
                value=existing.value,
                created_at=existing.created_at.isoformat() if existing.created_at else None,
                created_by=existing.created_by
            )

        # Create new option
        new_option = ServiceNameOption(
            id=str(uuid.uuid4()),
            value=option.value,
            created_by=current_user["id"]
        )
        db.add(new_option)
        db.commit()

        return DropdownOption(
            id=new_option.id,
            value=new_option.value,
            created_at=new_option.created_at.isoformat() if new_option.created_at else None,
            created_by=new_option.created_by
        )

# Software Menu Options
@router.get("/software-menus", response_model=List[DropdownOption], tags=["Dropdown Options"])
async def get_software_menu_options(current_user: dict = Depends(get_current_user)):
    """Get all software menu options."""
    with get_db() as db:
        options = db.query(SoftwareMenuOption).all()
        return [
            DropdownOption(
                id=option.id,
                value=option.value,
                created_at=option.created_at.isoformat() if option.created_at else None,
                created_by=option.created_by
            ) for option in options
        ]

@router.post("/software-menus", response_model=DropdownOption, tags=["Dropdown Options"])
async def create_software_menu_option(option: DropdownOptionCreate, current_user: dict = Depends(get_current_user)):
    """Create a new software menu option."""
    with get_db() as db:
        # Check if option already exists
        existing = db.query(SoftwareMenuOption).filter(SoftwareMenuOption.value == option.value).first()
        if existing:
            return DropdownOption(
                id=existing.id,
                value=existing.value,
                created_at=existing.created_at.isoformat() if existing.created_at else None,
                created_by=existing.created_by
            )

        # Create new option
        new_option = SoftwareMenuOption(
            id=str(uuid.uuid4()),
            value=option.value,
            created_by=current_user["id"]
        )
        db.add(new_option)
        db.commit()

        return DropdownOption(
            id=new_option.id,
            value=new_option.value,
            created_at=new_option.created_at.isoformat() if new_option.created_at else None,
            created_by=new_option.created_by
        )

# Issue Type Options
@router.get("/issue-types", response_model=List[IssueTypeDropdownOption], tags=["Dropdown Options"])
async def get_issue_type_options(
    software_menu_id: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """Get all issue type options, optionally filtered by software menu."""
    with get_db() as db:
        query = db.query(IssueTypeOption)
        if software_menu_id:
            query = query.filter(IssueTypeOption.software_menu_option_id == software_menu_id)
        options = query.all()
        return [
            IssueTypeDropdownOption(
                id=option.id,
                value=option.value,
                software_menu_option_id=option.software_menu_option_id,
                created_at=option.created_at.isoformat() if option.created_at else None,
                created_by=option.created_by
            ) for option in options
        ]

@router.post("/issue-types", response_model=IssueTypeDropdownOption, tags=["Dropdown Options"])
async def create_issue_type_option(option: IssueTypeDropdownOptionCreate, current_user: dict = Depends(get_current_user)):
    """Create a new issue type option."""
    with get_db() as db:
        # Check if option already exists for the given software_menu_option_id
        existing = db.query(IssueTypeOption).filter(
            IssueTypeOption.value == option.value,
            IssueTypeOption.software_menu_option_id == option.software_menu_option_id
        ).first()

        if existing:
            return IssueTypeDropdownOption(
                id=existing.id,
                value=existing.value,
                software_menu_option_id=existing.software_menu_option_id,
                created_at=existing.created_at.isoformat() if existing.created_at else None,
                created_by=existing.created_by
            )

        # Create new option
        new_option = IssueTypeOption(
            id=str(uuid.uuid4()),
            value=option.value,
            software_menu_option_id=option.software_menu_option_id,
            created_by=current_user["id"]
        )
        db.add(new_option)
        db.commit()

        return IssueTypeDropdownOption(
            id=new_option.id,
            value=new_option.value,
            software_menu_option_id=new_option.software_menu_option_id,
            created_at=new_option.created_at.isoformat() if new_option.created_at else None,
            created_by=new_option.created_by
        )
