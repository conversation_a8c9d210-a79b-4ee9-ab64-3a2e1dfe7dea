/* Global theme styles */
body.dark {
  background-color: #1F2937 !important;
  color: #F9FAFB !important;
}

body.dark .MuiPaper-root {
  background-color: #374151 !important;
  color: #F9FAFB !important;
}

body.dark .MuiTab-root {
  color: #D1D5DB !important;
}

body.dark .MuiTab-root.Mui-selected {
  color: #60A5FA !important;
}

body.dark .MuiTabs-indicator {
  background-color: #60A5FA !important;
}

body.dark .MuiTypography-root {
  color: #F9FAFB !important;
}

body.dark .MuiDivider-root {
  background-color: #4B5563 !important;
}

body.dark .MuiTableCell-root {
  color: #F9FAFB !important;
  border-bottom-color: #4B5563 !important;
}

body.dark .MuiTableHead-root .MuiTableCell-root {
  background-color: #374151 !important;
}

body.dark .MuiTableRow-root:hover {
  background-color: rgba(75, 85, 99, 0.2) !important;
}

body.dark .MuiButton-contained {
  background-color: #3B82F6 !important;
  color: #F9FAFB !important;
}

body.dark .MuiButton-outlined {
  border-color: #4B5563 !important;
  color: #F9FAFB !important;
}

body.dark .MuiButton-text {
  color: #60A5FA !important;
}

body.dark .MuiInputBase-root {
  color: #F9FAFB !important;
}

body.dark .MuiOutlinedInput-notchedOutline {
  border-color: #4B5563 !important;
}

body.dark .MuiInputLabel-root {
  color: #9CA3AF !important;
}

body.dark .MuiInputLabel-root.Mui-focused {
  color: #60A5FA !important;
}

body.dark .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #60A5FA !important;
}

/* App container */
.app-container {
  min-height: 100vh;
  display: flex;
  transition: background-color 0.3s ease;
}

body.dark .app-container {
  background-color: #1F2937;
}

/* Main content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: background-color 0.3s ease;
}

body.dark .main-content {
  background-color: #1F2937;
}

/* Sidebar */
.sidebar {
  transition: background-color 0.3s ease;
}

body.dark .sidebar {
  background-color: #111827;
  color: #F9FAFB;
  border-right-color: #374151;
}

body.dark .sidebar-header {
  border-bottom-color: #374151;
}

body.dark .sidebar-nav ul li a {
  color: #D1D5DB;
}

body.dark .sidebar-nav ul li a:hover {
  background-color: #374151;
}

body.dark .sidebar-nav ul li a.active {
  background-color: #3B82F6;
  color: #F9FAFB;
}

/* Topbar */
.topbar {
  transition: background-color 0.3s ease;
}

body.dark .topbar {
  background-color: #111827;
  border-bottom-color: #374151;
}

/* Notification bell */
body.dark .notification-bell-icon {
  color: #F9FAFB;
}

body.dark .notification-dropdown {
  background-color: #1F2937;
  border-color: #374151;
}

body.dark .notification-header {
  border-bottom-color: #374151;
}

body.dark .notification-item {
  border-bottom-color: #374151;
}

body.dark .notification-item:hover {
  background-color: #2D3748;
}

body.dark .notification-item.unread {
  background-color: #1E3A8A;
}

body.dark .notification-item.unread:hover {
  background-color: #1E40AF;
}

body.dark .notification-title {
  color: #F9FAFB;
}

body.dark .notification-message {
  color: #D1D5DB;
}

body.dark .notification-meta {
  color: #9CA3AF;
}

body.dark .notification-action-icon {
  color: #D1D5DB;
}

/* Dashboard components */
body.dark .dashboard-card {
  background-color: #374151;
  color: #F9FAFB;
}

body.dark .dashboard-card-header {
  border-bottom-color: #4B5563;
}

body.dark .dashboard-card-title {
  color: #F9FAFB;
}

body.dark .dashboard-card-subtitle {
  color: #D1D5DB;
}

/* Fix for Material UI components */
body.dark .MuiPaper-root.MuiCard-root {
  background-color: #374151;
}

body.dark .MuiCardHeader-title {
  color: #F9FAFB;
}

body.dark .MuiCardHeader-subheader {
  color: #D1D5DB;
}

body.dark .MuiCardContent-root {
  color: #F9FAFB;
}

body.dark .MuiCardActions-root {
  border-top-color: #4B5563;
}
