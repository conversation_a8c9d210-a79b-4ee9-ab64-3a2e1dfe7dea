import React, { useState, useMemo } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Pagination,
  Grid
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import FilterListIcon from '@mui/icons-material/FilterList';
import SearchIcon from '@mui/icons-material/Search';

const NotificationDetails = ({ stats, refreshData, isRefreshing }) => {
  // State
  const [filterOpen, setFilterOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [userFilter, setUserFilter] = useState([]);
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Filter out duplicate notifications
  const uniqueNotifications = useMemo(() => {
    const seen = new Map();
    return stats.notifications.filter(notification => {
      // Create a unique key based on document_id and type
      const key = `${notification.document_id}-${notification.type}`;

      // If we haven't seen this key before, keep the notification
      if (!seen.has(key)) {
        seen.set(key, true);
        return true;
      }

      return false;
    });
  }, [stats.notifications]);

  // Get document name for a notification
  const getDocumentName = (documentId) => {
    return stats.documents?.find(doc => doc?.id === documentId)?.filename || 'N/A';
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  // Get notification type color
  const getNotificationTypeColor = (type) => {
    switch (type) {
      case 'document_added':
        return 'success';
      case 'document_deleted':
        return 'error';
      case 'document_updated':
        return 'warning';
      default:
        return 'primary';
    }
  };

  // Check if a notification has been read by a user
  const isNotificationReadByUser = (notificationId, userId) => {
    const readStatus = stats.read_status.find(
      status => status.notification_id === notificationId && status.user_id === userId
    );
    return readStatus?.read_at !== null;
  };

  // Get unique notification types
  const notificationTypes = [...new Set(uniqueNotifications.map(n => n.type))];

  // Filter notifications based on search and type filters
  const filteredNotifications = uniqueNotifications
    .filter(notification => {
      // Apply search filter
      if (searchQuery && !notification.title.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }

      // Apply type filter
      if (typeFilter && notification.type !== typeFilter) {
        return false;
      }

      return true;
    });

  // Pagination
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(1);
  };

  // Get paginated data
  const paginatedNotifications = filteredNotifications.slice(
    (page - 1) * rowsPerPage,
    (page - 1) * rowsPerPage + rowsPerPage
  );

  return (
    <Box>
      {/* Header with controls */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Notification Details
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Toggle filters">
            <IconButton
              onClick={() => setFilterOpen(!filterOpen)}
              color={filterOpen ? 'primary' : 'default'}
            >
              <FilterListIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Refresh data">
            <IconButton
              onClick={refreshData}
              disabled={isRefreshing}
              sx={{
                animation: isRefreshing ? 'spin 1s linear infinite' : 'none',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }}
            >
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Filters */}
      {filterOpen && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Search Notifications"
                variant="outlined"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ color: 'action.active', mr: 1 }} />,
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth variant="outlined">
                <InputLabel id="notification-type-label">Notification Type</InputLabel>
                <Select
                  labelId="notification-type-label"
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  label="Notification Type"
                >
                  <MenuItem value="">All Types</MenuItem>
                  {notificationTypes.map(type => (
                    <MenuItem key={type} value={type}>
                      {type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth variant="outlined">
                <InputLabel id="user-filter-label">Filter Users</InputLabel>
                <Select
                  labelId="user-filter-label"
                  multiple
                  value={userFilter}
                  onChange={(e) => setUserFilter(e.target.value)}
                  label="Filter Users"
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => {
                        const user = stats.users.find(u => u.id === value);
                        return (
                          <Chip key={value} label={user ? user.username : value} size="small" />
                        );
                      })}
                    </Box>
                  )}
                >
                  {stats.users.map(user => (
                    <MenuItem key={user.id} value={user.id}>
                      {user.username}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Paper>
      )}

      {/* Table */}
      <TableContainer component={Paper} sx={{ mb: 3 }}>
        <Table sx={{ minWidth: 650 }} aria-label="notification details table">
          <TableHead>
            <TableRow>
              <TableCell>Notification</TableCell>
              <TableCell>Document</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Created</TableCell>
              <TableCell>Read By</TableCell>
              <TableCell>Unread By</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedNotifications.map((notification) => (
              <TableRow key={notification.id} hover>
                <TableCell component="th" scope="row">
                  {notification.title}
                </TableCell>
                <TableCell>{getDocumentName(notification.document_id)}</TableCell>
                <TableCell>
                  <Chip
                    label={notification.type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                    color={getNotificationTypeColor(notification.type)}
                    size="small"
                  />
                </TableCell>
                <TableCell>{formatDate(notification.created_at)}</TableCell>
                <TableCell>
                  {stats.users
                    .filter(user => isNotificationReadByUser(notification.id, user.id))
                    .map(user => user.username)
                    .join(', ')}
                </TableCell>
                <TableCell>
                  {stats.users
                    .filter(user => !isNotificationReadByUser(notification.id, user.id))
                    .map(user => user.username)
                    .join(', ')}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
            Rows per page:
          </Typography>
          <FormControl variant="standard" size="small">
            <Select
              value={rowsPerPage}
              onChange={handleChangeRowsPerPage}
              displayEmpty
            >
              <MenuItem value={5}>5</MenuItem>
              <MenuItem value={10}>10</MenuItem>
              <MenuItem value={25}>25</MenuItem>
              <MenuItem value={50}>50</MenuItem>
            </Select>
          </FormControl>
        </Box>
        <Pagination
          count={Math.ceil(filteredNotifications.length / rowsPerPage)}
          page={page}
          onChange={handleChangePage}
          color="primary"
        />
      </Box>
    </Box>
  );
};

export default NotificationDetails;
