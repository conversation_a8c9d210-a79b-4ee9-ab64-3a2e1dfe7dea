#!/bin/sh

# This script generates the env-config.js file with environment variables
# It's used in the Docker container to inject environment variables at runtime

# Recreate config file
rm -rf /usr/share/nginx/html/env-config.js
touch /usr/share/nginx/html/env-config.js

# Add assignment
echo "window._env_ = {" >> /usr/share/nginx/html/env-config.js

# Read each line in .env file
# Each line represents key=value pairs
if [ -f .env ]
then
  while read -r line || [ -n "$line" ];
  do
    # Split env variables by character `=`
    if printf '%s\n' "$line" | grep -q -e '='; then
      varname=$(printf '%s\n' "$line" | sed -e 's/=.*//')
      varvalue=$(printf '%s\n' "$line" | sed -e 's/^[^=]*=//')

      # Read value of current variable if exists as Environment variable
      value=$(printenv "$varname")
      # Otherwise use value from .env file
      [ -z "$value" ] && value=${varvalue}

      # Append configuration property to JS file
      echo "  $varname: \"$value\"," >> /usr/share/nginx/html/env-config.js
    fi
  done < .env
fi

# Add environment variables with REACT_APP prefix
for envvar in $(printenv | grep -E '^REACT_APP_' | sed -e 's/=.*//')
do
  value=$(printenv "$envvar")
  echo "  $envvar: \"$value\"," >> /usr/share/nginx/html/env-config.js
done

# Close the object
echo "};" >> /usr/share/nginx/html/env-config.js

# Run nginx
exec "$@"
