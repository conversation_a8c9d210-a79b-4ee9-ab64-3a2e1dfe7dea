import React, { useState, useEffect } from 'react';

/**
 * A component to display API connection errors with helpful troubleshooting information
 * 
 * @param {Object} props
 * @param {string} props.message - The error message to display
 * @param {string} props.apiUrl - The API URL that failed
 * @param {Function} props.onRetry - Function to call when retry button is clicked
 * @param {Function} props.onDismiss - Function to call when dismiss button is clicked
 */
const APIErrorAlert = ({ message, apiUrl, onRetry, onDismiss }) => {
  const [expanded, setExpanded] = useState(false);
  const [countdown, setCountdown] = useState(30);
  
  // Auto-retry countdown
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else if (countdown === 0 && onRetry) {
      onRetry();
      setCountdown(30);
    }
  }, [countdown, onRetry]);
  
  return (
    <div style={{
      position: 'fixed',
      top: '20px',
      left: '50%',
      transform: 'translateX(-50%)',
      width: '90%',
      maxWidth: '600px',
      backgroundColor: '#FEF2F2',
      borderRadius: '8px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      padding: '16px',
      zIndex: 1000,
      border: '1px solid #FCA5A5'
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'flex-start',
        justifyContent: 'space-between'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center'
        }}>
          <div style={{
            backgroundColor: '#FEE2E2',
            borderRadius: '50%',
            padding: '8px',
            marginRight: '12px'
          }}>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#DC2626" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="12"></line>
              <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
          </div>
          <div>
            <h3 style={{
              margin: '0 0 4px 0',
              fontSize: '16px',
              fontWeight: '600',
              color: '#991B1B'
            }}>
              API Connection Error
            </h3>
            <p style={{
              margin: '0',
              fontSize: '14px',
              color: '#7F1D1D'
            }}>
              {message || 'Unable to connect to the API server'}
            </p>
          </div>
        </div>
        <button
          onClick={onDismiss}
          style={{
            background: 'none',
            border: 'none',
            cursor: 'pointer',
            fontSize: '20px',
            color: '#9CA3AF',
            padding: '0'
          }}
        >
          &times;
        </button>
      </div>
      
      <div style={{ marginTop: '12px' }}>
        <button
          onClick={() => setExpanded(!expanded)}
          style={{
            background: 'none',
            border: 'none',
            cursor: 'pointer',
            fontSize: '14px',
            color: '#991B1B',
            padding: '0',
            textDecoration: 'underline',
            marginRight: '16px'
          }}
        >
          {expanded ? 'Hide troubleshooting' : 'Show troubleshooting'}
        </button>
        
        <button
          onClick={onRetry}
          style={{
            backgroundColor: '#DC2626',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            padding: '6px 12px',
            fontSize: '14px',
            cursor: 'pointer'
          }}
        >
          Retry now ({countdown}s)
        </button>
      </div>
      
      {expanded && (
        <div style={{
          marginTop: '16px',
          padding: '12px',
          backgroundColor: 'white',
          borderRadius: '4px',
          fontSize: '14px',
          color: '#4B5563'
        }}>
          <h4 style={{ margin: '0 0 8px 0', fontSize: '14px', fontWeight: '600' }}>Troubleshooting steps:</h4>
          <ul style={{ margin: '0 0 12px 0', paddingLeft: '20px' }}>
            <li>Check if the API server is running</li>
            <li>Verify the API URL: <code style={{ backgroundColor: '#F3F4F6', padding: '2px 4px', borderRadius: '2px' }}>{apiUrl}</code></li>
            <li>Check for CORS issues in the browser console</li>
            <li>Ensure your network connection is stable</li>
            <li>Try refreshing the page</li>
          </ul>
          <p style={{ margin: '0', fontSize: '12px' }}>
            If the problem persists, please contact your system administrator.
          </p>
        </div>
      )}
    </div>
  );
};

export default APIErrorAlert;
