/* Basic styles without Tailwind */
:root {
  --primary-color: #3B82F6;
  --primary-hover: #2563EB;
  --text-color: #1F2937;
  --text-light: #6B7280;
  --bg-color: #F3F4F6;
  --bg-card: #FFFFFF;
  --border-color: #E5E7EB;
  --success-color: #10B981;
  --warning-color: #F59E0B;
  --danger-color: #EF4444;
}

/* Dark mode variables */
.dark {
  --text-color: #F9FAFB;
  --text-light: #9CA3AF;
  --bg-color: #111827;
  --bg-card: #1F2937;
  --border-color: #374151;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-color);
  color: var(--text-color);
  overflow: hidden; /* Prevent system scrollbar */
}

/* Layout */
.app-container {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 250px;
  background-color: var(--bg-card);
  border-right: 1px solid var(--border-color);
  position: fixed;
  height: 100vh;
  z-index: 10;
  transition: transform 0.3s ease;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.sidebar-nav {
  padding: 1rem 0;
}

.sidebar-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav li {
  margin-bottom: 0.5rem;
}

.sidebar-nav a {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--text-color);
  text-decoration: none;
  border-radius: 0.375rem;
  transition: background-color 0.2s;
}

.sidebar-nav a:hover {
  background-color: var(--bg-color);
}

.sidebar-nav a.active {
  background-color: var(--primary-color);
  color: white;
}

.sidebar-nav a svg {
  margin-right: 0.75rem;
  width: 1.25rem;
  height: 1.25rem;
}

.main-content {
  flex: 1;
  margin-left: 250px;
  padding: 0.5rem; /* Reduced padding */
}

.topbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  background-color: var(--bg-card);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 5;
  height: 50px; /* Standard height */
}

/* Components */
.card {
  background-color: var(--bg-card);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.card-header {
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 1rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  border: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-secondary {
  background-color: var(--bg-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--border-color);
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  background-color: var(--bg-card);
  color: var(--text-color);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Table styles */
.table-container {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

th {
  font-weight: 600;
  color: var(--text-light);
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .mobile-menu-btn {
    display: block;
  }
}

@media (min-width: 769px) {
  .mobile-menu-btn {
    display: none;
  }
}

/* Visibility classes */
.sm-visible {
  display: none;
}

.md-visible {
  display: none;
}

@media (min-width: 640px) {
  .sm-visible {
    display: block !important;
  }

  .sm-grid-cols-2 {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (min-width: 768px) {
  .md-visible {
    display: block !important;
  }

  .md-block {
    display: block !important;
  }

  .md-flex-row {
    flex-direction: row !important;
  }

  .md-items-center {
    align-items: center !important;
  }

  .md-justify-between {
    justify-content: space-between !important;
  }

  .md-max-w-md {
    max-width: 28rem !important;
  }

  .md-grid-cols-2 {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (min-width: 1024px) {
  .lg-grid-cols-2 {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .lg-grid-cols-3 {
    grid-template-columns: repeat(3, 1fr) !important;
  }

  .lg-grid-cols-4 {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}
