import React, { useState, useEffect, useRef } from 'react';
import {
  PaperAirplaneIcon,
  ArrowPathIcon,
  Bars3Icon,
  XMarkIcon,
  ChatBubbleLeftRightIcon,
  ClockIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import { useChat } from '../../contexts/ChatContext';
import { useAuth } from '../../contexts/AuthContext';
import MarkdownRenderer from '../../components/common/MarkdownRenderer';
import ChatHistory from '../../components/chat/ChatHistory';
import DocumentReferences from '../../components/chat/DocumentReferences';
import './UserChat.css';
import '../admin/AdminChat.css';

const UserChat = () => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [showHistoryDropdown, setShowHistoryDropdown] = useState(false);
  const messagesEndRef = useRef(null);
  const dropdownRef = useRef(null);

  // Get dark mode from context or props
  const darkMode = document.documentElement.classList.contains('dark');

  // Get chat context
  const {
    messages,
    loading,
    error,
    initialized,
    conversations,
    currentConversationId,
    sendMessage,
    clearChat,
    initializeChat,
    formatTimestamp,
    loadConversation,
    startNewConversation
  } = useChat();

  // Get auth context
  const { user } = useAuth();

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Set sidebar to be visible by default on larger screens
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setShowSidebar(true);
      } else {
        setShowSidebar(false);
      }
    };

    // Set initial state
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowHistoryDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (message.trim() === '' || loading) return;

    const userMessage = message;
    setMessage('');
    setIsTyping(true);

    try {
      await sendMessage(userMessage);
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsTyping(false);
    }
  };

  return (
    <div className="modern-chat-container" 
    // style={{ height: 'calc(100vh - 65px)' }}
    >
      <div className="modern-chat-main">
        {/* Chat area */}
        <div className="modern-chat-content">

          {/* Chat header */}
          <div className="modern-chat-header">
            <div className="modern-chat-header-left">
              <div className="modern-ai-avatar">
                <span>AI</span>
              </div>

              <div className="modern-ai-info">
                <h3 className="modern-ai-name">Document AI Assistant</h3>
                <div className="modern-ai-status">
                  <div className={`modern-status-indicator ${initialized ? 'ready' : 'initializing'}`}></div>
                  <p className="modern-status-text">{initialized ? 'Ready' : 'Initializing...'}</p>
                </div>
              </div>
            </div>

            {/* Action buttons */}
            <div className="modern-chat-actions">
              {/* New chat button */}
              <button
                onClick={startNewConversation}
                className="modern-action-button"
                title="Start a new chat"
              >
                <ChatBubbleLeftRightIcon style={{ height: '1rem', width: '1rem' }} />
                <span>New Chat</span>
              </button>

              {/* Clear chat button */}
              <button
                onClick={clearChat}
                className="modern-action-button"
                title="Clear current chat"
              >
                <ArrowPathIcon style={{ height: '1rem', width: '1rem' }} />
                <span>Clear</span>
              </button>

              {/* Chat History Dropdown */}
              <div className="history-dropdown-container" ref={dropdownRef}>
                <button
                  onClick={() => setShowHistoryDropdown(!showHistoryDropdown)}
                  className="modern-action-button"
                  title="Show chat history"
                >
                  <ChatBubbleLeftRightIcon style={{ height: '1rem', width: '1rem' }} />
                  <span>History</span>
                  <ChevronDownIcon style={{ height: '0.75rem', width: '0.75rem', marginLeft: '0.25rem' }} />
                </button>

                {showHistoryDropdown && (
                  <div className="history-dropdown-content">
                    <ChatHistory />
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Messages */}
          <div className="modern-chat-messages">
            <div className="modern-message-list">
              {messages.length === 0 && initialized && (
                <div className="empty-state">
                  <div>
                    <h3 style={{
                      fontSize: '1.25rem',
                      fontWeight: '500',
                      marginBottom: '0.5rem',
                      color: darkMode ? '#F9FAFB' : '#111827'
                    }}>
                      Welcome to Document AI Chat
                    </h3>
                    <p style={{ fontSize: '0.875rem' }}>
                      Ask me anything about your documents and I'll help you find the information you need.
                    </p>
                  </div>
                </div>
              )}

              {messages.map((msg) => (
                <div
                  key={msg.id}
                  className={`modern-message-container ${msg.sender}`}
                >
                  <div className={`modern-message-bubble ${msg.sender}`}>
                    {msg.sender === 'bot' ? ( // Always use MarkdownRenderer for bot messages
                      <>
                        <div className="modern-message-content">
                          <MarkdownRenderer
                            content={msg.text}
                            style={{ whiteSpace: 'pre-wrap' }}
                          />
                        </div>
                        {msg.document_references && msg.document_references.length > 0 && (
                          <DocumentReferences
                            references={msg.document_references.map(r => ({
                              ...r,
                              link: r.link || r.url, // Prefer r.link, fallback to r.url
                              docname: r.docname || r.filename || 'Unnamed Document', // Prefer r.docname, fallback to r.filename
                            }))}
                            darkMode={darkMode}
                          />
                        )}
                      </>
                    ) : (
                      <div className="modern-message-content">
                        {msg.text}
                      </div>
                    )}
                    <div className="modern-message-timestamp">
                      {formatTimestamp(msg.timestamp)}
                    </div>
                  </div>
                </div>
              ))}

              {/* Typing indicator */}
              {isTyping && (
                <div className="modern-typing-indicator">
                  <div className="modern-typing-bubble">
                    <div className="modern-typing-dots">
                      <div className="modern-typing-dot"></div>
                      <div className="modern-typing-dot"></div>
                      <div className="modern-typing-dot"></div>
                    </div>
                  </div>
                </div>
              )}

              {/* Error message */}
              {error && (
                <div className="modern-error-container">
                  <div className="modern-error-message">
                    {error}
                  </div>
                </div>
              )}

              {/* Scroll anchor */}
              <div ref={messagesEndRef} />
            </div>
          </div>

          {/* Message input */}
          <div className="modern-chat-input-container">
            <form onSubmit={handleSendMessage} className="modern-chat-input-form">
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                disabled={loading || !initialized}
                className="modern-chat-input"
                placeholder={initialized ? "Ask a question about your documents..." : "Initializing AI..."}
                rows="1"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage(e);
                  }
                }}
              />
              <button
                type="submit"
                disabled={loading || !initialized || message.trim() === ''}
                className="modern-chat-send-button"
              >
                <PaperAirplaneIcon className="modern-send-icon" />
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserChat;
