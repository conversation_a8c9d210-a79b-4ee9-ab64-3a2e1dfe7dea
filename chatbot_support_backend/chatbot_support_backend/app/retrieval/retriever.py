"""
Document retrieval functionality for finding relevant context.
"""

import logging
from typing import List, Dict, Any, Optional, Union

from langchain.schema.document import Document
from langchain.schema.retriever import BaseRetriever
from langchain.retrievers import ContextualCompressionRetriever
from langchain.retrievers.document_compressors import LLMChainExtractor
from langchain_openai import ChatOpenAI

from app.embedding.vector_store import VectorStore

logger = logging.getLogger(__name__)


class DocumentRetriever:
    """
    Handles the retrieval of relevant document chunks for user queries.
    """

    def __init__(
        self,
        vector_store: VectorStore,
        use_compression: bool = False,
        llm_model_name: str = "gemini-2.0-flash-lite",
        top_k: int = 40,
        user_id: Optional[str] = None
    ):
        """
        Initialize the document retriever.

        Args:
            vector_store: The vector store to retrieve documents from
            use_compression: Whether to use contextual compression
            llm_model_name: The LLM model to use for compression
            top_k: Number of documents to retrieve
            user_id: Optional user ID for access control
        """
        self.vector_store = vector_store
        self.use_compression = use_compression
        self.top_k = top_k
        self.user_id = user_id

        # Create the base retriever if vector store is initialized
        if self.vector_store.vector_store is not None:
            self.base_retriever = self.vector_store.vector_store.as_retriever(
                search_type="similarity",
                search_kwargs={"k": top_k}
            )
        else:
            self.base_retriever = None
            logger.warning("Vector store not initialized. Retriever will be initialized when documents are added.")

        # Set up retriever if base_retriever is available
        if self.base_retriever is not None:
            # Set up compression if requested
            if use_compression:
                try:
                    from langchain_openai import ChatOpenAI
                    from langchain.retrievers import ContextualCompressionRetriever
                    from langchain.retrievers.document_compressors import LLMChainExtractor

                    llm = ChatOpenAI(model_name=llm_model_name, temperature=0)
                    compressor = LLMChainExtractor.from_llm(llm)
                    self.retriever = ContextualCompressionRetriever(
                        base_compressor=compressor,
                        base_retriever=self.base_retriever
                    )
                    logger.info(f"Using contextual compression with model: {llm_model_name}")
                except ImportError:
                    # Fall back to standard retrieval if imports fail
                    self.retriever = self.base_retriever
                    logger.warning("Failed to import compression modules, falling back to standard retrieval")
            else:
                self.retriever = self.base_retriever
                logger.info("Using standard retrieval without compression")
        else:
            self.retriever = None
            logger.warning("Retriever not initialized. Will return empty results until documents are added.")

    def retrieve(self, query: str, user_id: Optional[str] = None) -> List[Document]:
        """
        Retrieve relevant documents for a query.

        Args:
            query: The user query
            user_id: Optional user ID for access control (overrides the one set in constructor)

        Returns:
            List of relevant Document objects
        """
        try:
            # Check if retriever is initialized
            if self.retriever is None:
                # Try to initialize the retriever if vector store is available
                if self.vector_store and self.vector_store.vector_store is not None:
                    try:
                        # Create the base retriever
                        self.base_retriever = self.vector_store.vector_store.as_retriever(
                            search_type="similarity",
                            search_kwargs={"k": self.top_k}
                        )

                        # Set up compression if requested
                        if self.use_compression:
                            try:
                                from langchain_openai import ChatOpenAI
                                from langchain.retrievers import ContextualCompressionRetriever
                                from langchain.retrievers.document_compressors import LLMChainExtractor

                                llm = ChatOpenAI(model_name="gemini-2.0-flash-lite", temperature=0)
                                compressor = LLMChainExtractor.from_llm(llm)
                                self.retriever = ContextualCompressionRetriever(
                                    base_compressor=compressor,
                                    base_retriever=self.base_retriever
                                )
                                logger.info("Initialized retriever with contextual compression")
                            except ImportError:
                                # Fall back to standard retrieval if imports fail
                                self.retriever = self.base_retriever
                                logger.warning(
                                    "Failed to import compression modules, falling back to standard retrieval")
                        else:
                            self.retriever = self.base_retriever
                            logger.info("Initialized standard retriever without compression")
                    except Exception as init_error:
                        logger.error(f"Error initializing retriever: {str(init_error)}")
                        self.retriever = None

                # If retriever is still None, return empty results
                if self.retriever is None:
                    logger.warning(f"Retriever not initialized, returning empty results for query: {query}")
                    return []

            # Use the user_id from parameter or from constructor
            effective_user_id = user_id or self.user_id

            # Always delegate to vector_store's similarity_search, which should handle user_id filtering
            documents = self.vector_store.similarity_search(
                query=query,
                k=self.top_k,
                user_id=effective_user_id
            )
            logger.info(f"Retrieved {len(documents)} documents for query: {query} (user: {effective_user_id})")
            return documents
        except Exception as e:
            logger.error(f"Error retrieving documents: {str(e)}")
            # Return empty list instead of raising an exception
            return []

    def retrieve_with_scores(self, query: str, user_id: Optional[str] = None) -> List[tuple]:
        """
        Retrieve relevant documents with similarity scores.
        Relies on VectorStore's native filtering capabilities.

        Args:
            query: The user query
            user_id: Optional user ID for access control (overrides the one set in constructor)

        Returns:
            List of (Document, score) tuples
        """
        try:
            # Check if vector store is initialized (vector_store.vector_store is the VectorStore instance itself)
            if self.vector_store.vector_store is None:
                logger.warning(f"Vector store not initialized, returning empty results for query: {query}")
                return []

            # Use the user_id from parameter or from constructor
            effective_user_id = user_id or self.user_id

            # Directly call the VectorStore's method, passing user_id for native filtering.
            # The VectorStore's similarity_search_with_score will handle fetching accessible_docs
            # and constructing the where_filter for ChromaDB, or returning empty if no access.
            documents_with_scores = self.vector_store.similarity_search_with_score(
                query,
                k=self.top_k,  # Request top_k results, VectorStore handles filtering
                user_id=effective_user_id
            )

            # log the results
            if documents_with_scores:
                # log first document details for debugging
                if len(documents_with_scores) > 0:
                    first_doc, first_score = documents_with_scores[0]
                    logger.info(f"First retrieved document: {first_doc.metadata}, Score: {first_score}")
                else:
                    logger.info("No documents retrieved for query: {query} (user: {effective_user_id})")

            logger.info(
                f"Retrieved {len(documents_with_scores)} documents with scores for query: '{query[:50]}...' (user: {effective_user_id}) using native filtering via VectorStore.")
            return documents_with_scores
        except Exception as e:
            # Log with more context
            logger.error(
                f"Error retrieving documents with scores for user {effective_user_id if 'effective_user_id' in locals() else 'unknown'}, query '{query[:50]}...': {str(e)}", exc_info=True)
            # Return empty list instead of raising an exception
            return []
