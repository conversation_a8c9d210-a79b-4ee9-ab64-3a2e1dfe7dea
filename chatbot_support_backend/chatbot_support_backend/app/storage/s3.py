"""
S3 storage module for the RAG chatbot.
"""

import os
import uuid
import logging
import tempfile
from typing import Binary<PERSON>, Optional
import boto3
from botocore.exceptions import ClientError
from boto3.s3.transfer import TransferConfig

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class S3Storage:
    """
    S3 storage class for the RAG chatbot.
    """

    def __init__(self, bucket_name: Optional[str] = None, prefix: str = "chatbot_support"):
        """
        Initialize the S3 storage.

        Args:
            bucket_name: The S3 bucket name. If not provided, it will be read from the environment variable S3_BUCKET_NAME.
            prefix: The prefix to use for all S3 keys (default: "chatbot_support")
        """
        self.bucket_name = bucket_name or os.getenv("S3_BUCKET_NAME", "jmscpos-sandbox-chatbot-poc-bucket")
        if not self.bucket_name:
            raise ValueError("S3 bucket name not provided and S3_BUCKET_NAME environment variable not set")

        self.prefix = prefix
        if not self.prefix.endswith('/'):
            self.prefix += '/'

        # Get AWS credentials from environment variables with fallback values
        aws_access_key = os.getenv("AWS_ACCESS_KEY_ID")
        aws_secret_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        aws_region = os.getenv("AWS_REGION", "us-east-1")

        # Initialize S3 client with Signature Version 4
        if os.getenv('ENV', 'local') == 'local':
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=aws_access_key,
                aws_secret_access_key=aws_secret_key,
                # endpoint_url='http://localstack:4566',  # LocalStack endpoint
                region_name=aws_region,
                config=boto3.session.Config(signature_version='s3v4')  # Explicitly use Signature Version 4
            )
        else:
            self.s3_client = boto3.client(
                's3',
                region_name=aws_region,
                config=boto3.session.Config(signature_version='s3v4')  # Explicitly use Signature Version 4
            )

        # Ensure bucket exists
        self._ensure_bucket_exists()

    def _ensure_bucket_exists(self) -> None:
        """
        Ensure the S3 bucket exists.
        """
        try:
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            logger.info(f"S3 bucket {self.bucket_name} exists")
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == '404':
                # Bucket doesn't exist, create it
                try:
                    self.s3_client.create_bucket(
                        Bucket=self.bucket_name,
                        CreateBucketConfiguration={
                            'LocationConstraint': os.getenv("AWS_REGION", "us-east-1")
                        }
                    )
                    logger.info(f"Created S3 bucket {self.bucket_name}")
                except ClientError as create_error:
                    logger.error(f"Error creating S3 bucket: {str(create_error)}")
                    raise
            else:
                logger.error(f"Error checking S3 bucket: {str(e)}")
                raise

    def upload_file(self, file: BinaryIO, filename: str, content_type: str, use_original_name: bool = True) -> str:
        """
        Upload a file to S3.

        Args:
            file: The file object to upload
            filename: The original filename
            content_type: The content type of the file
            use_original_name: Whether to use the original filename in the S3 key

        Returns:
            The S3 key of the uploaded file
        """
        # Generate a key for the file
        file_extension = os.path.splitext(filename)[1]

        if use_original_name:
            # Use the original filename to make it easier to identify
            s3_key = f"{self.prefix}documents/{filename}"
        else:
            # Generate a unique key
            s3_key = f"{self.prefix}documents/{uuid.uuid4()}{file_extension}"

        try:
            # Upload the file
            self.s3_client.upload_fileobj(
                file,
                self.bucket_name,
                s3_key,
                ExtraArgs={
                    'ContentType': content_type,
                    'Metadata': {
                        'original_filename': filename
                    }
                }
            )
            logger.info(f"Uploaded file {filename} to S3 bucket {self.bucket_name} with key {s3_key}")
            return s3_key
        except ClientError as e:
            logger.error(f"Error uploading file to S3: {str(e)}")
            raise

    def download_file(self, s3_key: str) -> str:
        """
        Download a file from S3 to a temporary file.

        Args:
            s3_key: The S3 key of the file to download

        Returns:
            The path to the temporary file
        """
        try:
            # Create a temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(s3_key)[1])
            temp_file_path = temp_file.name
            temp_file.close()

            # Download the file
            self.s3_client.download_file(
                self.bucket_name,
                s3_key,
                temp_file_path
            )
            logger.info(f"Downloaded file {s3_key} from S3 bucket {self.bucket_name} to {temp_file_path}")
            return temp_file_path
        except ClientError as e:
            logger.error(f"Error downloading file from S3: {str(e)}")
            raise

    def delete_file(self, s3_key: str) -> None:
        """
        Delete a file from S3.

        Args:
            s3_key: The S3 key of the file to delete
        """
        try:
            # Delete the file
            self.s3_client.delete_object(
                Bucket=self.bucket_name,
                Key=s3_key
            )
            logger.info(f"Deleted file {s3_key} from S3 bucket {self.bucket_name}")
        except ClientError as e:
            logger.error(f"Error deleting file from S3: {str(e)}")
            raise

    def get_file_url(self, s3_key: str, expiration: int = 3600) -> str:
        """
        Get a presigned URL for a file in S3.

        Args:
            s3_key: The S3 key of the file
            expiration: The expiration time in seconds (default: 1 hour)

        Returns:
            The presigned URL
        """
        try:
            # Log the request details
            logger.info(f"Generating presigned URL for S3 key: {s3_key}, bucket: {self.bucket_name}, expiration: {expiration}s")

            params = {
                'Bucket': self.bucket_name,
                'Key': s3_key,
            }

            # Removed ServerSideEncryption parameter from get_object presigned URL generation
            # This parameter is not valid for get_object and causes validation errors.
            # KMS decryption is handled transparently by the presigned URL if permissions are correct.

            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params=params,
                ExpiresIn=expiration
            )

            # Log success and truncated URL for debugging
            logger.info(f"Successfully generated presigned URL for S3 key {s3_key}: {url[:50]}...")

            return url
        except Exception as e:
            # Catch all exceptions to provide better error handling
            logger.error(f"Error generating presigned URL for S3 key {s3_key}: {str(e)}", exc_info=True)
            
            # If an error occurs, re-raise the exception to indicate failure
            raise

    def list_files(self, subprefix: str = "documents/") -> list:
        """
        List files in the S3 bucket.

        Args:
            subprefix: The subprefix to filter files by (default: "documents/")

        Returns:
            A list of file information dictionaries
        """
        try:
            # Combine prefix with subprefix
            full_prefix = f"{self.prefix}{subprefix}"

            # List objects in the bucket
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=full_prefix
            )

            files = []
            if 'Contents' in response:
                for obj in response['Contents']:
                    files.append({
                        'key': obj['Key'],
                        'size': obj['Size'],
                        'last_modified': obj['LastModified'].isoformat()
                    })

            return files
        except ClientError as e:
            logger.error(f"Error listing files in S3: {str(e)}")
            raise

    def upload_file_obj_to_key(self, file_obj: BinaryIO, s3_key: str, content_type: str) -> str:
        """
        Upload a file-like object to a specific S3 key.

        Args:
            file_obj: The file-like object (e.g., io.BytesIO) to upload.
            s3_key: The full S3 key (including prefix and desired path) for the object.
            content_type: The content type of the file.

        Returns:
            The S3 key of the uploaded file.
        """
        try:
            # Get file size for logging
            if hasattr(file_obj, 'getbuffer'):
                file_size = file_obj.getbuffer().nbytes
            else:
                # For file objects that don't have get buffer
                current_pos = file_obj.tell()
                file_obj.seek(0, os.SEEK_END)
                file_size = file_obj.tell()
                file_obj.seek(current_pos)  # reset to original position
            
            logger.info(f"Uploading file object of size {file_size} bytes to S3 with key {s3_key}")

            # For larger files, use multipart upload with appropriate configuration
            config = TransferConfig(
                multipart_threshold=8 * 1024,  # 8 MB
                max_concurrency=10,
                multipart_chunksize=8 * 1024,  # 8 MB
                use_threads=True
            )

            self.s3_client.upload_fileobj(
                file_obj,
                self.bucket_name,
                s3_key,
                ExtraArgs={
                    'ContentType': content_type,
                },
                Config=config
            )
            logger.info(f"Uploaded file object to S3 bucket {self.bucket_name} with key {s3_key}")
            return s3_key
        except ClientError as e:
            logger.error(f"Error uploading file object to S3: {str(e)}")
            raise
