"""
User management module for the RAG chatbot.
"""

import os
import uuid
import hashlib
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Set

from app.database.db import get_db
from app.database.models import User as DBUser, Group as DBGroup, UserGroup, DocumentAccess, \
    ChatbotConfig, Conversation, Message

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# User roles
ROLE_ADMIN = "admin"
ROLE_USER = "user"

# User data directory - kept for backward compatibility
USER_DATA_DIR = Path("user_data")
USER_DATA_DIR.mkdir(exist_ok=True)

class UserManager:
    """
    User management class for the RAG chatbot.
    """

    def __init__(self):
        """
        Initialize the user manager.
        """
        self.users = {}
        self.groups = {}
        self.document_access = {}
        self.load_users()

        # Ensure database is initialized with at least one admin user
        self._ensure_admin_exists()

    def load_users(self) -> None:
        """
        Load users from the database.
        """
        try:
            with get_db() as db:
                # Load users
                db_users = db.query(DBUser).all()
                self.users = {}
                for user in db_users:
                    try:
                        # Get user groups
                        user_groups = [group.id for group in user.groups]

                        # Add to users dict
                        self.users[user.id] = {
                            "username": user.username,
                            "password_hash": user.password_hash,
                            "role": user.role,
                            "groups": user_groups,
                            "created_at": user.created_at.isoformat(),
                            "last_login": user.last_login.isoformat() if user.last_login else None
                        }
                    except Exception as user_e:
                        logger.error(f"Error processing user {user.id}: {str(user_e)}")

                # Load groups
                try:
                    db_groups = db.query(DBGroup).all()
                    self.groups = {}
                    for group in db_groups:
                        try:
                            # Get group members directly from the database
                            user_groups = db.query(UserGroup).filter(UserGroup.group_id == group.id).all()
                            group_members = []
                            for user_group in user_groups:
                                user = db.query(DBUser).filter(DBUser.id == user_group.user_id).first()
                                if user:
                                    group_members.append(user.id)

                            # Add to groups dict
                            self.groups[group.id] = {
                                "name": group.name,
                                "description": group.description or "",
                                "members": group_members
                            }

                            # Log group info for debugging
                            logger.info(f"Loaded group {group.name} with ID {group.id} and {len(group_members)} members")
                        except Exception as e:
                            logger.error(f"Error processing group {group.id}: {str(e)}")
                            # Print group attributes for debugging
                            for attr in dir(group):
                                if not attr.startswith('_'):
                                    try:
                                        logger.info(f"Group attribute {attr}: {getattr(group, attr)}")
                                    except Exception as attr_e:
                                        logger.error(f"Error getting attribute {attr}: {str(attr_e)}")
                except Exception as groups_e:
                    logger.error(f"Error loading groups: {str(groups_e)}")
                    self.groups = {}

                # Load document access
                self.document_access = {}
                try:
                    # Get all document access entries directly from the database
                    doc_access_entries = db.query(DocumentAccess).all()
                    for entry in doc_access_entries:
                        try:
                            doc_id = entry.document_id
                            if doc_id not in self.document_access:
                                self.document_access[doc_id] = {
                                    "groups": [],
                                    "updated_at": datetime.now().isoformat()
                                }
                            # Make sure we're using the correct attribute name
                            group_id = entry.group_id if hasattr(entry, 'group_id') else entry.id if hasattr(entry, 'id') else None
                            if group_id:
                                self.document_access[doc_id]["groups"].append(group_id)
                                logger.info(f"Added document access: doc_id={doc_id}, group_id={group_id}")
                            else:
                                logger.error(f"Could not determine group_id for document access entry: {entry}")
                        except Exception as entry_e:
                            logger.error(f"Error processing document access entry: {str(entry_e)}")
                            # Print entry attributes for debugging
                            for attr in dir(entry):
                                if not attr.startswith('_'):
                                    try:
                                        logger.info(f"Entry attribute {attr}: {getattr(entry, attr)}")
                                    except Exception as attr_e:
                                        logger.error(f"Error getting attribute {attr}: {str(attr_e)}")
                except Exception as doc_e:
                    logger.error(f"Error querying document access: {str(doc_e)}")

                logger.info(f"Loaded {len(self.users)} users from database")
        except Exception as e:
            logger.error(f"Error loading users from database: {str(e)}")
            # Initialize with empty data
            self.users = {}
            self.groups = {}
            self.document_access = {}

    def save_users(self) -> None:
        """
        Save users to the database.
        """
        # This method is kept for backward compatibility
        # The actual saving is done in the individual methods
        logger.info(f"User data is automatically saved to the database")

    def _ensure_admin_exists(self) -> None:
        """
        Ensure that at least one admin user exists in the database.
        """
        try:
            with get_db() as db:
                # Check if any admin user exists
                admin_exists = db.query(DBUser).filter(DBUser.role == ROLE_ADMIN).first() is not None

                if not admin_exists:
                    # Create default admin user and group
                    admin_id = str(uuid.uuid4())
                    admin_password = "admin"  # Default password

                    # Create admin user
                    admin_user = DBUser(
                        id=admin_id,
                        username="admin",
                        password_hash=self._hash_password(admin_password),
                        role=ROLE_ADMIN
                    )
                    db.add(admin_user)

                    # Create admin group
                    admin_group = DBGroup(
                        id=str(uuid.uuid4()),
                        name="Administrators",
                        description="Administrator group with full access"
                    )
                    db.add(admin_group)
                    db.flush()  # Flush to get IDs

                    # Add admin to admin group
                    user_group = UserGroup(
                        user_id=admin_user.id,
                        group_id=admin_group.id
                    )
                    db.add(user_group)

                    db.commit()
                    logger.info(f"Created default admin user with ID {admin_id} and group")
                else:
                    # Log the existing admin user ID for debugging
                    admin_user = db.query(DBUser).filter(DBUser.role == ROLE_ADMIN).first()
                    logger.info(f"Admin user already exists with ID {admin_user.id}")
        except Exception as e:
            logger.error(f"Error ensuring admin exists: {str(e)}")

    def _hash_password(self, password: str) -> str:
        """
        Hash a password using SHA-256.

        Args:
            password: The password to hash

        Returns:
            The hashed password
        """
        return hashlib.sha256(password.encode()).hexdigest()

    def authenticate(self, username: str, password: str) -> Optional[str]:
        """
        Authenticate a user.

        Args:
            username: The username
            password: The password

        Returns:
            The user ID if authentication is successful, None otherwise
        """
        try:
            with get_db() as db:
                # Find user by username
                user = db.query(DBUser).filter(DBUser.username == username).first()

                if user and user.password_hash == self._hash_password(password):
                    # Update last login
                    user.last_login = datetime.now()
                    db.commit()

                    # Update in-memory cache
                    self.users[user.id] = {
                        "username": user.username,
                        "password_hash": user.password_hash,
                        "role": user.role,
                        "groups": [group.id for group in user.groups],
                        "created_at": user.created_at.isoformat() if user.created_at else None,
                        "last_login": user.last_login.isoformat() if user.last_login else None
                    }

                    logger.info(f"User {username} authenticated successfully")
                    return user.id

                logger.warning(f"Failed authentication attempt for user {username}")
            return None
        except Exception as e:
            logger.error(f"Error authenticating user: {str(e)}")
            return None

    def create_user(self, username: str, password: str, role: str = ROLE_USER, groups: List[str] = None) -> Optional[str]:
        """
        Create a new user.

        Args:
            username: The username
            password: The password
            role: The user role (admin or user)
            groups: List of group IDs the user belongs to

        Returns:
            The user ID if creation is successful, None otherwise
        """
        try:
            with get_db() as db:
                # Check if username already exists
                existing_user = db.query(DBUser).filter(DBUser.username == username).first()
                if existing_user:
                    logger.warning(f"Username {username} already exists")
                    return None

                # Create user
                user_id = str(uuid.uuid4())
                new_user = DBUser(
                    id=user_id,
                    username=username,
                    password_hash=self._hash_password(password),
                    role=role
                )
                db.add(new_user)

                # Add user to groups
                if groups:
                    for group_id in groups:
                        # Check if group exists
                        group = db.query(DBGroup).filter(DBGroup.id == group_id).first()
                        if group:
                            # Create user-group association
                            user_group = UserGroup(
                                user_id=user_id,
                                group_id=group_id
                            )
                            db.add(user_group)

                db.commit()

                # Update in-memory cache
                self.users[user_id] = {
                    "username": username,
                    "password_hash": self._hash_password(password),
                    "role": role,
                    "groups": groups or [],
                    "created_at": datetime.now().isoformat(),
                    "last_login": None
                }

                # Update groups in-memory cache
                if groups:
                    for group_id in groups:
                        if group_id in self.groups:
                            if "members" not in self.groups[group_id]:
                                self.groups[group_id]["members"] = []
                            self.groups[group_id]["members"].append(user_id)

                logger.info(f"Created user {username} with ID {user_id}")
                return user_id
        except Exception as e:
            logger.error(f"Error creating user: {str(e)}")
            return None

    def delete_user(self, user_id: str) -> bool:
        """
        Delete a user.

        Args:
            user_id: The user ID

        Returns:
            True if deletion is successful, False otherwise
        """
        try:
            with get_db() as db:
                # Find user
                user = db.query(DBUser).filter(DBUser.id == user_id).first()
                if not user:
                    logger.warning(f"User ID {user_id} not found")
                    return False

                # First, explicitly delete all user_groups associations
                # This is necessary because we have a composite primary key
                user_groups = db.query(UserGroup).filter(UserGroup.user_id == user_id).all()
                for user_group in user_groups:
                    db.delete(user_group)

                # Delete any document access entries related to groups the user is in
                # (This is optional as CASCADE should handle it, but being explicit)
                for group in user.groups:
                    doc_access = db.query(DocumentAccess).filter(DocumentAccess.group_id == group.id).all()
                    for access in doc_access:
                        db.delete(access)

                # Delete any chatbot configs for this user
                chatbot_configs = db.query(ChatbotConfig).filter(ChatbotConfig.user_id == user_id).all()
                for config in chatbot_configs:
                    db.delete(config)

                # Delete any conversations for this user
                conversations = db.query(Conversation).filter(Conversation.user_id == user_id).all()
                for conversation in conversations:
                    # Delete all messages in the conversation
                    messages = db.query(Message).filter(Message.conversation_id == conversation.id).all()
                    for message in messages:
                        db.delete(message)
                    db.delete(conversation)

                # Now delete the user
                db.delete(user)
                db.commit()

                # Update in-memory cache
                if user_id in self.users:
                    del self.users[user_id]

                # Remove user from groups in-memory cache
                for group_id, group in self.groups.items():
                    if "members" in group and user_id in group["members"]:
                        group["members"].remove(user_id)

                logger.info(f"Deleted user with ID {user_id}")
                return True
        except Exception as e:
            logger.error(f"Error deleting user: {str(e)}")
            # Try to rollback the transaction
            try:
                db.rollback()
            except:
                pass
            return False

    def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a user by ID.

        Args:
            user_id: The user ID

        Returns:
            The user data if found, None otherwise
        """
        try:
            with get_db() as db:
                # Find user in database
                user = db.query(DBUser).filter(DBUser.id == user_id).first()

                if user:
                    # Get user groups
                    user_groups = [group.id for group in user.groups]

                    # Get group details for each group
                    group_details = []
                    for group in user.groups:
                        group_details.append({
                            "id": group.id,
                            "name": group.name,
                            "description": group.description or ""
                        })

                    # Create user data dictionary
                    user_data = {
                        "id": user.id,
                        "username": user.username,
                        "role": user.role,
                        "groups": user_groups,
                        "group_details": group_details,  # Add group details
                        "created_at": user.created_at.isoformat() if user.created_at else None,
                        "last_login": user.last_login.isoformat() if user.last_login else None
                    }

                    # Update in-memory cache
                    self.users[user_id] = user_data.copy()
                    self.users[user_id]["password_hash"] = user.password_hash

                    return user_data

                # If not found in database, check in-memory cache as fallback
                if user_id in self.users:
                    user_data = self.users[user_id].copy()
                    # Don't return password hash
                    if "password_hash" in user_data:
                        del user_data["password_hash"]

                    # Try to add group details if not present
                    if "groups" in user_data and "group_details" not in user_data:
                        group_details = []
                        for group_id in user_data["groups"]:
                            # Try to get group from database
                            group = db.query(DBGroup).filter(DBGroup.id == group_id).first()
                            if group:
                                group_details.append({
                                    "id": group.id,
                                    "name": group.name,
                                    "description": group.description or ""
                                })

                        if group_details:
                            user_data["group_details"] = group_details

                    return user_data

                return None
        except Exception as e:
            logger.error(f"Error getting user: {str(e)}")
            # Fallback to in-memory cache
            if user_id in self.users:
                user_data = self.users[user_id].copy()
                # Don't return password hash
                if "password_hash" in user_data:
                    del user_data["password_hash"]
                return user_data
            return None

    def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """
        Get a user by username.

        Args:
            username: The username

        Returns:
            The user data if found, None otherwise
        """
        try:
            with get_db() as db:
                # Find user in database
                user = db.query(DBUser).filter(DBUser.username == username).first()

                if user:
                    # Get user groups
                    user_groups = [group.id for group in user.groups]

                    # Get group details for each group
                    group_details = []
                    for group in user.groups:
                        group_details.append({
                            "id": group.id,
                            "name": group.name,
                            "description": group.description or ""
                        })

                    # Create user data dictionary
                    user_data = {
                        "id": user.id,
                        "username": user.username,
                        "role": user.role,
                        "groups": user_groups,
                        "group_details": group_details,  # Add group details
                        "created_at": user.created_at.isoformat() if user.created_at else None,
                        "last_login": user.last_login.isoformat() if user.last_login else None
                    }

                    # Update in-memory cache
                    self.users[user.id] = user_data.copy()
                    self.users[user.id]["password_hash"] = user.password_hash

                    return user_data

                # If not found in database, check in-memory cache as fallback
                for user_id, user in self.users.items():
                    if user["username"] == username:
                        user_data = user.copy()
                        user_data["id"] = user_id
                        # Don't return password hash
                        if "password_hash" in user_data:
                            del user_data["password_hash"]

                        # Try to add group details if not present
                        if "groups" in user_data and "group_details" not in user_data:
                            group_details = []
                            for group_id in user_data["groups"]:
                                # Try to get group from database
                                group = db.query(DBGroup).filter(DBGroup.id == group_id).first()
                                if group:
                                    group_details.append({
                                        "id": group.id,
                                        "name": group.name,
                                        "description": group.description or ""
                                    })

                            if group_details:
                                user_data["group_details"] = group_details

                        return user_data

                return None
        except Exception as e:
            logger.error(f"Error getting user by username: {str(e)}")
            # Fallback to in-memory cache
            for user_id, user in self.users.items():
                if user["username"] == username:
                    user_data = user.copy()
                    user_data["id"] = user_id
                    # Don't return password hash
                    if "password_hash" in user_data:
                        del user_data["password_hash"]
                    return user_data
            return None

    def update_user(self, user_id: str, data: Dict[str, Any]) -> bool:
        """
        Update a user.

        Args:
            user_id: The user ID
            data: The data to update

        Returns:
            True if update is successful, False otherwise
        """
        try:
            with get_db() as db:
                # Find user in database
                user = db.query(DBUser).filter(DBUser.id == user_id).first()

                if not user:
                    logger.warning(f"User ID {user_id} not found")
                    return False

                # Update user data
                # Update user data (username, password, role)
                for key, value in data.items():
                    if key == "password":
                        user.password_hash = self._hash_password(value)
                    elif key == "username":
                        user.username = value
                    elif key == "role":
                        user.role = value
                    # Do NOT handle 'groups' here directly, it will be handled separately below

                # Handle group updates
                if "groups" in data and data["groups"] is not None:
                    new_group_ids = set(data["groups"])
                    current_group_ids = {group.id for group in user.groups}

                    # Groups to add
                    groups_to_add = new_group_ids - current_group_ids
                    for group_id in groups_to_add:
                        # Use the existing add_user_to_group method
                        self.add_user_to_group(user_id, group_id)
                        logger.info(f"Added user {user_id} to group {group_id}")

                    # Groups to remove
                    groups_to_remove = current_group_ids - new_group_ids
                    for group_id in groups_to_remove:
                        # Use the existing remove_user_from_group method
                        self.remove_user_from_group(user_id, group_id)
                        logger.info(f"Removed user {user_id} from group {group_id}")

                db.commit() # Commit all changes including user details and group associations

                # Update in-memory cache if it exists
                if user_id in self.users:
                    for key, value in data.items():
                        if key == "password":
                            self.users[user_id]["password_hash"] = self._hash_password(value)
                        elif key == "groups":
                            # Update groups in cache directly
                            self.users[user_id]["groups"] = list(new_group_ids)
                        elif key != "password_hash":  # Don't allow direct password hash update
                            self.users[user_id][key] = value

                logger.info(f"Updated user with ID {user_id}")
                return True
        except Exception as e:
            logger.error(f"Error updating user: {str(e)}")
            # Rollback the transaction in case of any error
            try:
                db.rollback()
            except Exception as rb_e:
                logger.error(f"Error during rollback: {str(rb_e)}")
            return False

    def create_group(self, name: str, description: str = "") -> Optional[str]:
        """
        Create a new group.

        Args:
            name: The group name
            description: The group description

        Returns:
            The group ID if creation is successful, None otherwise
        """
        try:
            with get_db() as db:
                # Check if group name already exists
                existing_group = db.query(DBGroup).filter(DBGroup.name == name).first()
                if existing_group:
                    logger.warning(f"Group name {name} already exists")
                    return None

                # Create group
                group_id = str(uuid.uuid4())
                new_group = DBGroup(
                    id=group_id,
                    name=name,
                    description=description
                )
                db.add(new_group)
                db.commit()

                # Update in-memory cache
                self.groups[group_id] = {
                    "name": name,
                    "description": description,
                    "members": []
                }

                logger.info(f"Created group {name} with ID {group_id}")
                return group_id
        except Exception as e:
            logger.error(f"Error creating group: {str(e)}")
            return None

    def update_group(self, group_id: str, data: Dict[str, Any]) -> bool:
        """
        Update a group.

        Args:
            group_id: The group ID
            data: The data to update

        Returns:
            True if update is successful, False otherwise
        """
        try:
            with get_db() as db:
                # Find group in database
                group = db.query(DBGroup).filter(DBGroup.id == group_id).first()

                if not group:
                    logger.warning(f"Group ID {group_id} not found")
                    return False

                # Update group data
                for key, value in data.items():
                    if key == "name":
                        # Check if the new name already exists in another group
                        if value != group.name:  # Only check if name is actually changing
                            existing_group = db.query(DBGroup).filter(DBGroup.name == value, DBGroup.id != group_id).first()
                            if existing_group:
                                logger.warning(f"Group name {value} already exists for another group")
                                return False
                        group.name = value
                    elif key == "description":
                        group.description = value

                db.commit()

                # Update in-memory cache if it exists
                if group_id in self.groups:
                    for key, value in data.items():
                        if key in ["name", "description"]:
                            self.groups[group_id][key] = value

                logger.info(f"Updated group with ID {group_id}")
                return True
        except Exception as e:
            logger.error(f"Error updating group: {str(e)}")
            return False

    def delete_group(self, group_id: str) -> bool:
        """
        Delete a group.

        Args:
            group_id: The group ID

        Returns:
            True if deletion is successful, False otherwise
        """
        try:
            with get_db() as db:
                # Find group
                group = db.query(DBGroup).filter(DBGroup.id == group_id).first()
                if not group:
                    logger.warning(f"Group ID {group_id} not found")
                    return False

                # First, remove all document access entries for this group
                db.query(DocumentAccess).filter(DocumentAccess.group_id == group_id).delete()

                # Then, remove all user-group associations for this group
                db.query(UserGroup).filter(UserGroup.group_id == group_id).delete()

                # Now delete the group itself
                db.query(DBGroup).filter(DBGroup.id == group_id).delete()

                # Commit the changes
                db.commit()

                # Update in-memory cache
                if group_id in self.groups:
                    del self.groups[group_id]

                # Remove group from users in-memory cache
                for user_id, user in self.users.items():
                    if group_id in user["groups"]:
                        user["groups"].remove(group_id)

                # Remove group from document access in-memory cache
                for doc_id, access in self.document_access.items():
                    if group_id in access["groups"]:
                        access["groups"].remove(group_id)

                logger.info(f"Deleted group with ID {group_id}")
                return True
        except Exception as e:
            logger.error(f"Error deleting group: {str(e)}")
            return False

    def get_group(self, group_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a group by ID.

        Args:
            group_id: The group ID

        Returns:
            The group data if found, None otherwise
        """
        try:
            with get_db() as db:
                # Find group in database
                group = db.query(DBGroup).filter(DBGroup.id == group_id).first()

                if group:
                    # Get group members
                    group_members = [member.id for member in group.members]

                    # Create group data dictionary
                    group_data = {
                        "id": group.id,
                        "name": group.name,
                        "description": group.description or "",
                        "members": group_members
                    }

                    # Update in-memory cache
                    self.groups[group_id] = group_data.copy()

                    return group_data

                # If not found in database, check in-memory cache as fallback
                if group_id in self.groups:
                    return self.groups[group_id].copy()

                return None
        except Exception as e:
            logger.error(f"Error getting group: {str(e)}")
            # Fallback to in-memory cache
            if group_id in self.groups:
                return self.groups[group_id].copy()
            return None

    def get_all_groups(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all groups.

        Returns:
            Dictionary of all groups
        """
        try:
            with get_db() as db:
                # Get all groups from database
                db_groups = db.query(DBGroup).all()

                # Convert to dictionary
                groups_dict = {}
                for group in db_groups:
                    # Get group members
                    group_members = [member.id for member in group.members]

                    # Add to dictionary
                    groups_dict[group.id] = {
                        "name": group.name,
                        "description": group.description or "",
                        "members": group_members
                    }

                    # Update in-memory cache
                    self.groups[group.id] = groups_dict[group.id].copy()

                return groups_dict
        except Exception as e:
            logger.error(f"Error getting all groups: {str(e)}")
            # Fallback to in-memory cache
            return self.groups.copy()

    def get_all_users(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all users.

        Returns:
            Dictionary of all users
        """
        try:
            with get_db() as db:
                # Get all users from database
                db_users = db.query(DBUser).all()

                # Convert to dictionary
                users_dict = {}
                for user in db_users:
                    # Get user groups
                    user_groups = [group.id for group in user.groups]

                    # Get group details for each group
                    group_details = []
                    for group in user.groups:
                        group_details.append({
                            "id": group.id,
                            "name": group.name,
                            "description": group.description or ""
                        })

                    # Add to dictionary
                    users_dict[user.id] = {
                        "username": user.username,
                        "role": user.role,
                        "groups": user_groups,
                        "group_details": group_details,  # Add group details
                        "created_at": user.created_at.isoformat() if user.created_at else None,
                        "last_login": user.last_login.isoformat() if user.last_login else None
                    }

                    # Update in-memory cache
                    self.users[user.id] = users_dict[user.id].copy()
                    self.users[user.id]["password_hash"] = user.password_hash

                return users_dict
        except Exception as e:
            logger.error(f"Error getting all users: {str(e)}")

            # Fallback to in-memory cache
            users_copy = {}
            for user_id, user in self.users.items():
                users_copy[user_id] = user.copy()
                if "password_hash" in users_copy[user_id]:
                    del users_copy[user_id]["password_hash"]

                # Try to add group details if not present
                if "groups" in users_copy[user_id] and "group_details" not in users_copy[user_id]:
                    try:
                        group_details = []
                        for group_id in users_copy[user_id]["groups"]:
                            # Try to get group from database
                            group = db.query(DBGroup).filter(DBGroup.id == group_id).first()
                            if group:
                                group_details.append({
                                    "id": group.id,
                                    "name": group.name,
                                    "description": group.description or ""
                                })

                        if group_details:
                            users_copy[user_id]["group_details"] = group_details
                    except Exception as group_error:
                        logger.error(f"Error getting group details for user {user_id}: {str(group_error)}")

            return users_copy

    def add_user_to_group(self, user_id: str, group_id: str) -> bool:
        """
        Add a user to a group.

        Args:
            user_id: The user ID
            group_id: The group ID

        Returns:
            True if addition is successful, False otherwise
        """
        try:
            with get_db() as db:
                # Check if user exists
                user = db.query(DBUser).filter(DBUser.id == user_id).first()
                if not user:
                    logger.warning(f"User ID {user_id} not found")
                    return False

                # Check if group exists
                group = db.query(DBGroup).filter(DBGroup.id == group_id).first()
                if not group:
                    logger.warning(f"Group ID {group_id} not found")
                    return False

                # Check if user is already in group
                user_group = db.query(UserGroup).filter(
                    UserGroup.user_id == user_id,
                    UserGroup.group_id == group_id
                ).first()

                if not user_group:
                    # Add user to group
                    user_group = UserGroup(
                        user_id=user_id,
                        group_id=group_id
                    )
                    db.add(user_group)
                    db.commit()

                # Update in-memory cache
                # Add group to user
                if user_id in self.users and group_id not in self.users[user_id]["groups"]:
                    self.users[user_id]["groups"].append(group_id)

                # Add user to group
                if group_id in self.groups:
                    if "members" not in self.groups[group_id]:
                        self.groups[group_id]["members"] = []

                    if user_id not in self.groups[group_id]["members"]:
                        self.groups[group_id]["members"].append(user_id)

                logger.info(f"Added user {user_id} to group {group_id}")
                return True
        except Exception as e:
            logger.error(f"Error adding user to group: {str(e)}")
            return False

    def remove_user_from_group(self, user_id: str, group_id: str) -> bool:
        """
        Remove a user from a group.

        Args:
            user_id: The user ID
            group_id: The group ID

        Returns:
            True if removal is successful, False otherwise
        """
        try:
            with get_db() as db:
                # Check if user exists
                user = db.query(DBUser).filter(DBUser.id == user_id).first()
                if not user:
                    logger.warning(f"User ID {user_id} not found")
                    return False

                # Check if group exists
                group = db.query(DBGroup).filter(DBGroup.id == group_id).first()
                if not group:
                    logger.warning(f"Group ID {group_id} not found")
                    return False

                # Remove user from group
                user_group = db.query(UserGroup).filter(
                    UserGroup.user_id == user_id,
                    UserGroup.group_id == group_id
                ).first()

                if user_group:
                    db.delete(user_group)
                    db.commit()

                # Update in-memory cache
                # Remove group from user
                if user_id in self.users and group_id in self.users[user_id]["groups"]:
                    self.users[user_id]["groups"].remove(group_id)

                # Remove user from group
                if group_id in self.groups and "members" in self.groups[group_id] and user_id in self.groups[group_id]["members"]:
                    self.groups[group_id]["members"].remove(user_id)

                logger.info(f"Removed user {user_id} from group {group_id}")
                return True
        except Exception as e:
            logger.error(f"Error removing user from group: {str(e)}")
            return False

    def set_document_access(self, doc_id: str, groups: List[str]) -> bool:
        """
        Set document access for groups.

        Args:
            doc_id: The document ID (UUID from the documents table)
            groups: List of group IDs with access

        Returns:
            True if setting is successful, False otherwise
        """
        try:
            with get_db() as db:
                # Check if document exists
                from app.database.models import Document as DBDocument
                document = db.query(DBDocument).filter(DBDocument.id == doc_id).first()
                if not document:
                    logger.warning(f"Document ID {doc_id} not found")
                    return False

                # Validate groups
                for group_id in groups:
                    group = db.query(DBGroup).filter(DBGroup.id == group_id).first()
                    if not group:
                        logger.warning(f"Group ID {group_id} not found")
                        return False

                # Delete existing access records
                db.query(DocumentAccess).filter(DocumentAccess.document_id == doc_id).delete()

                # Create new access records
                for group_id in groups:
                    doc_access = DocumentAccess(
                        document_id=doc_id,
                        group_id=group_id
                    )
                    db.add(doc_access)

                db.commit()

                # Update in-memory cache
                self.document_access[doc_id] = {
                    "groups": groups.copy(),
                    "updated_at": datetime.now().isoformat()
                }

                logger.info(f"Set document {doc_id} access for {len(groups)} groups")
                return True
        except Exception as e:
            logger.error(f"Error setting document access: {str(e)}")
            return False

    def get_document_access(self, doc_id: str) -> List[str]:
        """
        Get groups with access to a document.

        Args:
            doc_id: The document ID (UUID from the documents table)

        Returns:
            List of group IDs with access
        """
        try:
            with get_db() as db:
                # Check if document exists
                from app.database.models import Document as DBDocument
                document = db.query(DBDocument).filter(DBDocument.id == doc_id).first()
                if not document:
                    logger.warning(f"Document ID {doc_id} not found")
                    return []

                # Get document access records
                access_records = db.query(DocumentAccess).filter(DocumentAccess.document_id == doc_id).all()

                # Extract group IDs
                group_ids = [record.group_id for record in access_records]

                # Update in-memory cache
                if group_ids and doc_id not in self.document_access:
                    self.document_access[doc_id] = {
                        "groups": group_ids,
                        "updated_at": datetime.now().isoformat()
                    }

                return group_ids
        except Exception as e:
            logger.error(f"Error getting document access: {str(e)}")
            # Fallback to in-memory cache
            if doc_id in self.document_access:
                return self.document_access[doc_id]["groups"]
            return []

    def user_has_document_access(self, user_id: str, doc_id: str) -> bool:
        """
        Check if a user has access to a document.

        Args:
            user_id: The user ID
            doc_id: The document ID (UUID from the documents table)

        Returns:
            True if the user has access, False otherwise
        """
        try:
            with get_db() as db:
                # Check if user exists
                user = db.query(DBUser).filter(DBUser.id == user_id).first()
                if not user:
                    logger.warning(f"User ID {user_id} not found")
                    return False

                # Admins have access to all documents
                if user.role == ROLE_ADMIN:
                    return True

                # Check if document exists
                from app.database.models import Document as DBDocument
                document = db.query(DBDocument).filter(DBDocument.id == doc_id).first()
                if not document:
                    logger.warning(f"Document ID {doc_id} not found")
                    return False

                # Check if document has access control defined
                access_records = db.query(DocumentAccess).filter(DocumentAccess.document_id == doc_id).all()
                if not access_records:
                    # If no access control is set for this document, non-admins do not have access.
                    # Admin access is handled earlier.
                    return False

                # Check if user is in any group that has access to this document
                user_group_ids = {group.id for group in user.groups} # Get IDs of groups the user belongs to
                document_group_ids = {access.group_id for access in access_records} # Get IDs of groups allowed for this doc
                
                # Check for any overlap between the user's groups and the document's allowed groups
                has_access = not user_group_ids.isdisjoint(document_group_ids)

                return has_access
        except Exception as e:
            logger.error(f"Error checking document access: {str(e)}")
            # Fallback to in-memory cache
            if user_id not in self.users:
                return False

            # Admins have access to all documents
            if self.users[user_id]["role"] == ROLE_ADMIN:
                return True

            # Check if document has access control defined in the cache
            if doc_id not in self.document_access:
                # If no access control is set in the cache for this document,
                # non-admins do not have access. Admin access is handled earlier.
                return False

            # Check if user is in any group with access
            user_groups = self.users[user_id]["groups"]
            doc_groups = self.document_access[doc_id]["groups"]

            return any(group_id in doc_groups for group_id in user_groups)

    def get_accessible_documents(self, user_id: str) -> Set[str]:
        """
        Get all documents a user has access to.

        Args:
            user_id: The user ID

        Returns:
            Set of document IDs the user has access to
        """
        try:
            with get_db() as db:
                # Find user in database
                user = db.query(DBUser).filter(DBUser.id == user_id).first()

                if not user:
                    logger.warning(f"User ID {user_id} not found")
                    return set()

                # Import Document model
                from app.database.models import Document as DBDocument

                # Admins have access to all documents
                if user.role == ROLE_ADMIN:
                    # Get all document IDs
                    doc_records = db.query(DBDocument.id).all()
                    accessible_docs = {record[0] for record in doc_records}
                    logger.info(f"Admin user {user_id} has access to ALL documents. Count: {len(accessible_docs)}")
                    return accessible_docs

                # Get user's groups
                user_group_ids = [group.id for group in user.groups]
                logger.info(f"User {user_id} belongs to groups: {user_group_ids}")

                # Get documents user has access to through their groups
                doc_access_records = db.query(DocumentAccess.document_id).filter(
                    DocumentAccess.group_id.in_(user_group_ids)
                ).distinct().all()

                # For non-admins, only return documents explicitly assigned to their groups.
                # Documents not in DocumentAccess are considered restricted.
                accessible_docs = {record[0] for record in doc_access_records}
                logger.info(f"User {user_id} has access to documents: {accessible_docs}")
                return accessible_docs
        except Exception as e:
            logger.error(f"Error getting accessible documents for user {user_id}: {str(e)}", exc_info=True)
            # Fallback to in-memory cache
            if user_id not in self.users:
                logger.warning(f"User {user_id} not found in cache during error fallback.")
                return set()

            # Admins have access to all documents
            if self.users[user_id]["role"] == ROLE_ADMIN:
                accessible_docs_from_cache = set(self.document_access.keys())
                logger.info(f"Admin user {user_id} (cache fallback) has access to ALL documents. Count: {len(accessible_docs_from_cache)}")
                return accessible_docs_from_cache

            # Get user groups from cache
            user_groups = self.users[user_id]["groups"]
            logger.info(f"User {user_id} (cache fallback) belongs to groups: {user_groups}")

            # Get documents user has access to from cache
            accessible_docs = set()
            for doc_id, access in self.document_access.items():
                doc_groups = access["groups"]
                if any(group_id in doc_groups for group_id in user_groups):
                    accessible_docs.add(doc_id)
            logger.info(f"User {user_id} (cache fallback) has access to documents: {accessible_docs}")
            return accessible_docs

    def is_admin(self, user_id: str) -> bool:
        """
        Check if a user is an admin.

        Args:
            user_id: The user ID

        Returns:
            True if the user is an admin, False otherwise
        """
        try:
            with get_db() as db:
                # Find user in database
                user = db.query(DBUser).filter(DBUser.id == user_id).first()

                if user:
                    # Update in-memory cache
                    if user_id not in self.users:
                        # Get user groups
                        user_groups = [group.id for group in user.groups]

                        # Create user data dictionary
                        self.users[user_id] = {
                            "username": user.username,
                            "password_hash": user.password_hash,
                            "role": user.role,
                            "groups": user_groups,
                            "created_at": user.created_at.isoformat() if user.created_at else None,
                            "last_login": user.last_login.isoformat() if user.last_login else None
                        }

                    return user.role == ROLE_ADMIN

                # If not found in database, check in-memory cache as fallback
                if user_id in self.users:
                    return self.users[user_id]["role"] == ROLE_ADMIN

                logger.warning(f"User ID {user_id} not found")
                return False
        except Exception as e:
            logger.error(f"Error checking if user is admin: {str(e)}")
            # Fallback to in-memory cache
            if user_id in self.users:
                return self.users[user_id]["role"] == ROLE_ADMIN
            return False
