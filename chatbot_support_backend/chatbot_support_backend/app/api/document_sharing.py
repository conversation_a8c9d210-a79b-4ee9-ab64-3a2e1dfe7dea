"""
Document sharing module for ensuring documents are properly shared with users.

This module provides functions to ensure that when a document is uploaded and assigned to groups,
all users in those groups have the document properly added to their vector stores.
"""

import os
import logging
from typing import List, Set, Dict, Any, Optional
from pathlib import Path

from app.database.db import get_db
from app.database.models import Document as DBDocument
from app.database.models import DocumentAccess, UserGroup
from app.storage.chatbot_storage import ChatbotStorage
from app.auth.user import UserManager
from app.chatbot import RAGChatbot
from app.retrieval.retriever import DocumentRetriever

# Set up logging
logger = logging.getLogger(__name__)

def get_users_in_groups(group_ids: List[str]) -> Set[str]:
    """
    Get all users that belong to any of the specified groups.

    Args:
        group_ids: List of group IDs

    Returns:
        Set of user IDs
    """
    if not group_ids:
        return set()

    try:
        with get_db() as db:
            # Get all users in these groups
            user_records = db.query(UserGroup.user_id).filter(
                UserGroup.group_id.in_(group_ids)
            ).distinct().all()

            # Extract user IDs
            user_ids = {record[0] for record in user_records}

            logger.info(f"Found {len(user_ids)} users in groups {group_ids}")
            return user_ids
    except Exception as e:
        logger.error(f"Error getting users in groups: {str(e)}")
        return set()

def ensure_document_in_user_vector_stores(
    document_id: str,
    group_ids: List[str],
    chatbot_storage: ChatbotStorage,
    user_data_dir: Path,
    google_service_account_file: Optional[str] = None,
    google_project_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Ensure that a document is properly added to the vector stores of all users in the specified groups.

    This function:
    1. Gets all users in the specified groups
    2. For each user, checks if they have a chatbot instance
    3. If not, creates a new chatbot instance
    4. Processes the document and adds it to the user's vector store
    5. Reinitializes the retriever to ensure it uses the updated vector store

    Args:
        document_id: The ID of the document to share
        group_ids: List of group IDs to share the document with
        chatbot_storage: ChatbotStorage instance
        user_data_dir: Path to the user data directory
        google_service_account_file: Path to Google service account JSON file
        google_project_id: Google Cloud project ID

    Returns:
        Dictionary with results of the operation
    """
    results = {
        "document_id": document_id,
        "groups": group_ids,
        "users_processed": 0,
        "users_updated": 0,
        "errors": []
    }

    try:
        # Get the document details
        with get_db() as db:
            document = db.query(DBDocument).filter(DBDocument.id == document_id).first()

            if not document:
                error_msg = f"Document with ID {document_id} not found"
                logger.error(error_msg)
                results["errors"].append(error_msg)
                return results

            # Store document details
            filename = document.filename
            s3_key = document.s3_key
            results["filename"] = filename
            results["s3_key"] = s3_key

        # Get all users in the specified groups
        user_ids = get_users_in_groups(group_ids)
        results["total_users"] = len(user_ids)

        if not user_ids:
            logger.warning(f"No users found in groups {group_ids}, nothing to update")
            return results

        # Process the document for each user
        for user_id in user_ids:
            results["users_processed"] += 1

            try:
                # Get the user's chatbot
                chatbot = chatbot_storage.get(user_id)

                # If the user doesn't have a chatbot, create one
                if not chatbot:
                    # Get the vector store path for this user
                    vector_store_dir = os.path.join(user_data_dir, user_id, "vector_store")

                    # Ensure the vector store directory exists and has correct permissions
                    os.makedirs(vector_store_dir, exist_ok=True)
                    try:
                        os.chmod(vector_store_dir, 0o777)
                        logger.info(f"Set permissions on vector store directory: {vector_store_dir}")
                    except Exception as e:
                        logger.warning(f"Could not set permissions on vector store directory {vector_store_dir}: {str(e)}")

                    logger.info(f"Creating new chatbot for user {user_id} with vector store at {vector_store_dir}")

                    # Create a new chatbot with HuggingFace embeddings and Google LLM
                    # Check if the Google credentials file exists
                    if google_service_account_file and os.path.exists(google_service_account_file):
                        logger.info(f"Using Google service account file at {google_service_account_file}")
                    else:
                        # Try to find the file in alternative locations
                        possible_paths = [
                            "/app/google_credentials.json",
                            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "google_credentials.json"),
                            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), "google_credentials.json")
                        ]

                        for path in possible_paths:
                            if os.path.exists(path):
                                logger.info(f"Found Google service account file at alternative path: {path}")
                                google_service_account_file = path
                                break

                    chatbot = RAGChatbot(
                        use_google_embeddings=False,  # Use HuggingFace embeddings instead
                        use_google_llm=True,  # Still use Google for LLM
                        google_service_account_file=google_service_account_file,
                        google_project_id=google_project_id,
                        vector_store_type="chroma",
                        vector_store_dir=vector_store_dir
                    )

                    # Store the chatbot
                    chatbot_storage.store(user_id, chatbot)
                    logger.info(f"Created and stored new chatbot for user {user_id}")

                # Process the document
                documents = chatbot.pdf_processor.process_pdf(
                    filename,  # We just need the filename for metadata
                    s3_key=s3_key
                )

                if not documents:
                    logger.warning(f"No content extracted from document {filename} for user {user_id}")
                    continue

                # Add document ID to metadata
                for doc in documents:
                    doc.metadata["document_id"] = document_id
                    # Ensure filename is in metadata
                    doc.metadata["file_name"] = filename
                    # Ensure S3 key is in metadata
                    if s3_key:
                        doc.metadata["s3_key"] = s3_key

                # Add documents to vector store
                chatbot.vector_store.add_documents(documents)

                # Save the vector store to ensure persistence
                chatbot.save_vector_store()
                logger.info(f"Added document {document_id} to vector store for user {user_id}")

                # Reinitialize retriever
                retriever_success = chatbot.initialize_retriever()
                if retriever_success:
                    logger.info(f"Reinitialized retriever for user {user_id}")
                else:
                    logger.warning(f"Failed to reinitialize retriever for user {user_id}")

                # Update the chatbot in storage
                chatbot_storage.update(user_id, chatbot)
                logger.info(f"Updated chatbot for user {user_id} in storage")

                results["users_updated"] += 1

            except Exception as e:
                error_msg = f"Error updating chatbot for user {user_id}: {str(e)}"
                logger.error(error_msg)
                results["errors"].append(error_msg)

        logger.info(f"Document {document_id} shared with {results['users_updated']} out of {len(user_ids)} users")
        return results

    except Exception as e:
        error_msg = f"Error sharing document {document_id} with users: {str(e)}"
        logger.error(error_msg)
        results["errors"].append(error_msg)
        return results
