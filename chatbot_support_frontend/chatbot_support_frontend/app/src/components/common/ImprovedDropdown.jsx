import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { useTheme } from '../../contexts/ThemeContext';

/**
 * ImprovedDropdown component that allows selecting from a list of options
 * and adding new options with a more professional UI.
 */
const ImprovedDropdown = ({
  id,
  label,
  value,
  onChange,
  options,
  onAddOption,
  placeholder = 'Select an option',
  addNewText = '+ Add new',
  disabled = false,
  required = false,
  error = null,
  className = '',
  maxDisplayItems, // Destructure the new prop
}) => {
  const { darkMode } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [newValue, setNewValue] = useState('');
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [searchTerm, setSearchTerm] = useState(''); // New state for search term
  const dropdownRef = useRef(null);
  const inputRef = useRef(null);
  const searchInputRef = useRef(null); // New ref for search input

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
        setIsAddingNew(false);
      }
    };

    // Only add the event listener when the dropdown is open
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      // Focus the search input when the dropdown opens
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    } else {
      // Clear search term when dropdown closes
      setSearchTerm('');
    }
  }, [isOpen]);

  // Focus input when adding new option
  useEffect(() => {
    if (isAddingNew && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isAddingNew]);

  const handleToggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      setIsAddingNew(false);
      setSearchTerm(''); // Clear search term on toggle
    }
  };

  const handleSelectOption = (option) => {
    onChange(option.value);
    setIsOpen(false);
    setSearchTerm(''); // Clear search term on selection
  };

  const handleAddNewClick = (e) => {
    e.stopPropagation();
    setIsAddingNew(true);
  };

  const handleAddNewSubmit = async (e) => {
    if (e) {
      e.preventDefault(); // Prevent form submission and page refresh
      e.stopPropagation(); // Prevent event bubbling
    }

    if (newValue.trim()) {
      try {
        // Show loading state
        const form = e?.target;
        const submitButton = form?.querySelector('button[type="submit"]');
        if (submitButton) {
          submitButton.disabled = true;
          submitButton.textContent = 'Adding...';
        }

        console.log('Adding new option:', newValue.trim());

        // Add the new option to the database
        const newOption = await onAddOption(newValue.trim());

        console.log('Response from adding option:', newOption);

        // Update the local state with the new value
        if (newOption && newOption.value) {
          onChange(newOption.value);

          // Reset the form
          setNewValue('');
          setIsAddingNew(false);
          setIsOpen(false);

          console.log('Successfully added new option:', newOption.value);
        } else {
          console.error('Invalid response from adding option:', newOption);
          alert('There was a problem adding the new option. Please try again.');
        }
      } catch (error) {
        console.error('Error adding new option:', error);
        // Show error message to user
        alert('Failed to add new option. Please try again.');
      } finally {
        // Reset button state
        const form = e?.target;
        const submitButton = form?.querySelector('button[type="submit"]');
        if (submitButton) {
          submitButton.disabled = false;
          submitButton.textContent = 'Add';
        }
      }
    }
  };

  const handleNewValueChange = (e) => {
    setNewValue(e.target.value);
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      setIsOpen(false);
      setIsAddingNew(false);
      setSearchTerm('');
    }
  };

  // Filtered and limited options
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const displayedOptions = filteredOptions.slice(0, maxDisplayItems);

  // Find the selected option
  const selectedOption = options.find(option => option.value === value);

  // Styles for the dropdown
  const dropdownStyles = {
    container: {
      position: 'relative',
      marginBottom: '1rem',
      width: '100%',
      ...className && { className },
    },
    label: {
      display: 'block',
      fontSize: '0.875rem',
      fontWeight: '500',
      marginBottom: '0.5rem',
      color: darkMode ? '#D1D5DB' : '#374151',
    },
    selectButton: {
      width: '100%',
      padding: '0.5rem',
      borderRadius: '0.25rem',
      border: `1px solid ${darkMode ? '#4B5563' : '#D1D5DB'}`,
      backgroundColor: darkMode ? '#374151' : '#F9FAFB',
      color: darkMode ? '#D1D5DB' : '#374151',
      fontSize: '0.875rem',
      textAlign: 'left',
      cursor: disabled ? 'not-allowed' : 'pointer',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      opacity: disabled ? 0.7 : 1,
    },
    dropdownMenu: {
      position: 'absolute',
      zIndex: 10,
      width: '100%',
      marginTop: '0.25rem',
      borderRadius: '0.25rem',
      border: `1px solid ${darkMode ? '#4B5563' : '#D1D5DB'}`,
      backgroundColor: darkMode ? '#374151' : '#FFFFFF',
      boxShadow: '0 2px 5px rgba(0, 0, 0, 0.15)',
      maxHeight: '200px',
      overflowY: 'auto',
    },
    searchInput: {
      width: 'calc(100% - 1rem)',
      padding: '0.5rem',
      margin: '0.5rem',
      borderRadius: '0.25rem',
      border: `1px solid ${darkMode ? '#4B5563' : '#D1D5DB'}`,
      backgroundColor: darkMode ? '#1F2937' : '#FFFFFF',
      color: darkMode ? '#D1D5DB' : '#374151',
      fontSize: '0.875rem',
    },
    option: {
      padding: '0.5rem',
      cursor: 'pointer',
      fontSize: '0.875rem',
      color: darkMode ? '#D1D5DB' : '#374151',
    },
    selectedOption: {
      backgroundColor: darkMode ? '#4B5563' : '#E5E7EB',
    },
    hoverOption: {
      backgroundColor: darkMode ? '#4B5563' : '#F3F4F6',
    },
    addNewOption: {
      padding: '0.5rem',
      cursor: 'pointer',
      fontSize: '0.875rem',
      color: darkMode ? '#60A5FA' : '#2563EB',
      borderTop: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`,
    },
    addNewForm: {
      display: 'flex',
      padding: '0.5rem',
      borderTop: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`,
    },
    addNewInput: {
      flex: 1,
      padding: '0.25rem 0.5rem',
      fontSize: '0.875rem',
      border: `1px solid ${darkMode ? '#4B5563' : '#D1D5DB'}`,
      borderRadius: '0.25rem 0 0 0.25rem',
      backgroundColor: darkMode ? '#1F2937' : '#FFFFFF',
      color: darkMode ? '#D1D5DB' : '#374151',
    },
    addNewButton: {
      padding: '0.25rem 0.5rem',
      fontSize: '0.875rem',
      backgroundColor: darkMode ? '#3B82F6' : '#2563EB',
      color: '#FFFFFF',
      border: 'none',
      borderRadius: '0 0.25rem 0.25rem 0',
      cursor: 'pointer',
    },
    errorText: {
      color: '#EF4444',
      fontSize: '0.75rem',
      marginTop: '0.25rem',
    },
    chevronIcon: {
      width: '16px',
      height: '16px',
      transition: 'transform 0.2s',
      transform: isOpen ? 'rotate(180deg)' : 'rotate(0)',
    }
  };

  return (
    <div style={dropdownStyles.container} ref={dropdownRef} onKeyDown={handleKeyDown}>
      {label && (
        <label htmlFor={id} style={dropdownStyles.label}>
          {label} {required && <span style={{ color: '#EF4444' }}>*</span>}
        </label>
      )}

      <button
        type="button"
        id={id}
        onClick={handleToggleDropdown}
        disabled={disabled}
        style={dropdownStyles.selectButton}
      >
        <span>{selectedOption ? selectedOption.label : placeholder}</span>
        <svg
          style={dropdownStyles.chevronIcon}
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>

      {error && (
        <p style={dropdownStyles.errorText}>{error}</p>
      )}

      {isOpen && (
        <div style={dropdownStyles.dropdownMenu}>
          <input
            ref={searchInputRef}
            type="text"
            placeholder="Search..."
            value={searchTerm}
            onChange={handleSearchChange}
            onClick={(e) => e.stopPropagation()} // Prevent closing dropdown when clicking in search input
            style={dropdownStyles.searchInput}
          />
          {displayedOptions.length > 0 ? (
            displayedOptions.map((option) => (
              <div
                key={option.value}
                onClick={() => handleSelectOption(option)}
                style={{
                  ...dropdownStyles.option,
                  ...(option.value === value ? dropdownStyles.selectedOption : {}),
                }}
                onMouseOver={(e) => {
                  if (option.value !== value) {
                    e.currentTarget.style.backgroundColor = dropdownStyles.hoverOption.backgroundColor;
                  }
                }}
                onMouseOut={(e) => {
                  if (option.value !== value) {
                    e.currentTarget.style.backgroundColor = '';
                  }
                }}
              >
                {option.label}
              </div>
            ))
          ) : (
            <div style={{ ...dropdownStyles.option, cursor: 'default', color: darkMode ? '#9CA3AF' : '#6B7280' }}>
              No options found.
            </div>
          )}

          {!isAddingNew ? (
            <div
              style={dropdownStyles.addNewOption}
              onClick={handleAddNewClick}
            >
              {addNewText}
            </div>
          ) : (
            <form
              onSubmit={(e) => {
                e.preventDefault(); // Prevent form submission and page refresh
                handleAddNewSubmit(e);
              }}
              style={dropdownStyles.addNewForm}
              onClick={(e) => e.stopPropagation()} // Prevent closing dropdown when clicking in the form
            >
              <input
                ref={inputRef}
                type="text"
                value={newValue}
                onChange={handleNewValueChange}
                style={dropdownStyles.addNewInput}
                placeholder="Enter new value"
                onClick={(e) => e.stopPropagation()} // Prevent closing dropdown when clicking in the input
              />
              <button
                type="button" // Changed from "submit" to "button" to prevent automatic form submission
                style={dropdownStyles.addNewButton}
                onClick={(e) => {
                  e.preventDefault(); // Prevent form submission
                  e.stopPropagation(); // Prevent closing dropdown when clicking the button
                  handleAddNewSubmit(e); // Manually call the submit handler
                }}
              >
                Add
              </button>
            </form>
          )}
        </div>
      )}
    </div>
  );
};

ImprovedDropdown.propTypes = {
  id: PropTypes.string.isRequired,
  label: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    })
  ).isRequired,
  onAddOption: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  addNewText: PropTypes.string,
  disabled: PropTypes.bool,
  required: PropTypes.bool,
  error: PropTypes.string,
  className: PropTypes.string,
  maxDisplayItems: PropTypes.number, // New prop type
};

export default ImprovedDropdown;
