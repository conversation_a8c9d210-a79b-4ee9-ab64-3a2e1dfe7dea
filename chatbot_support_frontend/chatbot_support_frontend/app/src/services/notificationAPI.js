import { API_BASE_URL, getAuthToken, handleResponse } from './api';

/**
 * API service for notifications
 */
const notificationAPI = {
  /**
   * Get notifications for the current user
   * @param {number} limit - Maximum number of notifications to return
   * @param {boolean} includeRead - Whether to include read notifications
   * @returns {Promise<Object>} - Notifications data
   */
  getNotifications: async (limit = 20, includeRead = false) => {
    try {
      const response = await fetch(`${API_BASE_URL}/notifications?limit=${limit}&include_read=${includeRead}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      throw error;
    }
  },

  /**
   * Get unread notification count
   * @returns {Promise<Object>} - Unread count data
   */
  getUnreadCount: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/notifications/unread-count`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching unread count:', error);
      throw error;
    }
  },

  /**
   * Check for new notifications efficiently
   * @param {string} lastChecked - ISO timestamp of when notifications were last checked
   * @returns {Promise<Object>} - Object with has_new boolean
   */
  checkNewNotifications: async (lastChecked = null) => {
    try {
      const url = lastChecked
        ? `${API_BASE_URL}/notifications/check-new?last_checked=${encodeURIComponent(lastChecked)}`
        : `${API_BASE_URL}/notifications/check-new`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      return await handleResponse(response);
    } catch (error) {
      console.error('Error checking for new notifications:', error);
      // Return false instead of throwing to avoid disrupting the UI
      return { has_new: false };
    }
  },

  /**
   * Mark a notification as read
   * @param {string} notificationId - ID of the notification
   * @returns {Promise<Object>} - Success message
   */
  markAsRead: async (notificationId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/notifications/${notificationId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          is_read: true
        }),
      });

      return await handleResponse(response);
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  },

  /**
   * Mark all notifications as read
   * @returns {Promise<Object>} - Success message
   */
  markAllAsRead: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/notifications/mark-all-read`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      return await handleResponse(response);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  },

  /**
   * Delete a notification
   * @param {string} notificationId - ID of the notification
   * @returns {Promise<Object>} - Success message
   */
  deleteNotification: async (notificationId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/notifications/${notificationId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      return await handleResponse(response);
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  },

  /**
   * Create a notification (admin only)
   * @param {Object} notification - Notification data
   * @returns {Promise<Object>} - Created notification
   */
  createNotification: async (notification) => {
    try {
      const response = await fetch(`${API_BASE_URL}/notifications`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(notification),
      });

      return await handleResponse(response);
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  },

  /**
   * Get dashboard analytics data (admin only)
   * @returns {Promise<Object>} - Dashboard analytics data
   */
  getDashboardAnalytics: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/analytics/dashboard`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching dashboard analytics:', error);
      throw error;
    }
  },

  /**
   * Get notification read statistics (admin only)
   * @returns {Promise<Object>} - Notification statistics data
   */
  getNotificationStats: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/analytics/notification-stats`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching notification statistics:', error);
      throw error;
    }
  },

  /**
   * Get activities for a specific user (admin only)
   * @param {string} userId - ID of the user
   * @param {number} limit - Maximum number of activities to return
   * @returns {Promise<Object>} - User activities data
   */
  getUserActivities: async (userId, limit = 50) => {
    try {
      const response = await fetch(`${API_BASE_URL}/analytics/user-activities/${userId}?limit=${limit}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching user activities:', error);
      throw error;
    }
  },

  /**
   * Get activities for a specific document (admin only)
   * @param {string} documentId - ID of the document
   * @param {number} limit - Maximum number of activities to return
   * @returns {Promise<Object>} - Document activities data
   */
  getDocumentActivities: async (documentId, limit = 50) => {
    try {
      const response = await fetch(`${API_BASE_URL}/analytics/document-activities/${documentId}?limit=${limit}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching document activities:', error);
      throw error;
    }
  },
};

export default notificationAPI;
