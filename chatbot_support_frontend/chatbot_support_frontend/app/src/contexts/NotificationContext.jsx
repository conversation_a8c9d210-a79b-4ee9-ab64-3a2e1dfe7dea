import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import notificationAPI from '../services/notificationAPI';
import { useAuth } from './AuthContext';

// Create the context
const NotificationContext = createContext();

// Custom hook to use the notification context
export const useNotification = () => useContext(NotificationContext);

// Notification provider component
export const NotificationProvider = ({ children }) => {
  const { isAuthenticated, user } = useAuth();
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [hasNewNotifications, setHasNewNotifications] = useState(false);

  // Use a ref to store the last check timestamp
  const lastCheckedRef = useRef(null);

  // Fetch unread count - define this first to avoid circular dependency
  const fetchUnreadCount = useCallback(async () => {
    if (!isAuthenticated) return;

    try {
      const response = await notificationAPI.getUnreadCount();

      if (response && response.count !== undefined) {
        setUnreadCount(response.count);
      }
    } catch (error) {
      console.error('Error fetching unread count:', error);
      // Don't set error state here to avoid UI disruption
    }
  }, [isAuthenticated]);

  // Fetch notifications
  const fetchNotifications = useCallback(async (includeRead = false, silent = false) => {
    if (!isAuthenticated) return;

    try {
      // Only show loading indicator if not in silent mode
      if (!silent) {
        setLoading(true);
      }
      setError(null);

      // Update the last checked timestamp
      lastCheckedRef.current = new Date().toISOString();

      const response = await notificationAPI.getNotifications(20, includeRead);

      if (response && response.notifications) {
        // Update the notifications state
        setNotifications(response.notifications);

        // Reset the new notifications flag when we explicitly fetch notifications
        if (!silent) {
          setHasNewNotifications(false);
        }
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
      if (!silent) {
        setError('Failed to fetch notifications');
      }
    } finally {
      if (!silent) {
        setLoading(false);
      }
    }
  }, [isAuthenticated]);

  // Check for new notifications efficiently
  const checkForNewNotifications = useCallback(async () => {
    if (!isAuthenticated) return;

    try {
      // First, check if there are new notifications
      const response = await notificationAPI.checkNewNotifications(lastCheckedRef.current);

      if (response && response.has_new) {
        setHasNewNotifications(true);
        // Update the unread count
        await fetchUnreadCount();
        // Also fetch the actual notifications to keep the list updated
        await fetchNotifications(true, true);
      } else {
        // Even if there are no new notifications, periodically refresh the unread count
        // This ensures the badge stays in sync
        await fetchUnreadCount();
      }

      // Update the last checked timestamp
      lastCheckedRef.current = new Date().toISOString();
    } catch (error) {
      console.error('Error checking for new notifications:', error);
      // Don't set error state here to avoid UI disruption

      // Try to fetch unread count anyway to keep the UI in sync
      try {
        await fetchUnreadCount();
      } catch (countError) {
        console.error('Error fetching unread count:', countError);
      }
    }
  }, [isAuthenticated, fetchUnreadCount, fetchNotifications]);

  // Mark notification as read
  const markAsRead = async (notificationId) => {
    try {
      setLoading(true);

      await notificationAPI.markAsRead(notificationId);

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, is_read: true }
            : notification
        )
      );

      // Update unread count
      fetchUnreadCount();

      return true;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      setError('Failed to mark notification as read');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      setLoading(true);

      await notificationAPI.markAllAsRead();

      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, is_read: true }))
      );

      // Update unread count
      setUnreadCount(0);

      return true;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      setError('Failed to mark all notifications as read');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Delete notification
  const deleteNotification = async (notificationId) => {
    try {
      setLoading(true);

      await notificationAPI.deleteNotification(notificationId);

      // Update local state
      setNotifications(prev =>
        prev.filter(notification => notification.id !== notificationId)
      );

      // Update unread count
      fetchUnreadCount();

      return true;
    } catch (error) {
      console.error('Error deleting notification:', error);
      setError('Failed to delete notification');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Create notification (admin only)
  const createNotification = async (notification) => {
    if (!isAuthenticated || user?.role !== 'admin') {
      setError('Only admins can create notifications');
      return false;
    }

    try {
      setLoading(true);

      const response = await notificationAPI.createNotification(notification);

      // Refresh notifications
      fetchNotifications();

      return response;
    } catch (error) {
      console.error('Error creating notification:', error);
      setError('Failed to create notification');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Fetch dashboard analytics (admin only)
  const fetchDashboardAnalytics = async () => {
    if (!isAuthenticated || user?.role !== 'admin') {
      setError('Only admins can access dashboard analytics');
      return null;
    }

    try {
      setLoading(true);

      const response = await notificationAPI.getDashboardAnalytics();

      return response;
    } catch (error) {
      console.error('Error fetching dashboard analytics:', error);
      setError('Failed to fetch dashboard analytics');
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch and polling setup - combined to avoid dependency issues
  useEffect(() => {
    if (!isAuthenticated) return;

    // Initial fetch with error handling
    const initialFetch = async () => {
      try {
        // First get the unread count
        await fetchUnreadCount();
        // Then fetch notifications silently
        await fetchNotifications(true, true);
      } catch (error) {
        console.error("Error during initial notification fetch:", error);
      }
    };

    // Execute initial fetch
    initialFetch();

    // No automatic polling - we'll only check when the user clicks the bell icon
    // or after specific actions like document upload/deletion

    // No interval to clean up
    return () => {};
  }, [isAuthenticated, fetchNotifications, fetchUnreadCount, checkForNewNotifications]);

  // Reset hasNewNotifications when notifications are viewed
  const resetNewNotificationsFlag = useCallback(() => {
    setHasNewNotifications(false);
  }, []);

  // Context value
  const value = {
    notifications,
    unreadCount,
    loading,
    error,
    hasNewNotifications,
    fetchNotifications,
    fetchUnreadCount,
    checkForNewNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    createNotification,
    fetchDashboardAnalytics,
    resetNewNotificationsFlag
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export default NotificationContext;
