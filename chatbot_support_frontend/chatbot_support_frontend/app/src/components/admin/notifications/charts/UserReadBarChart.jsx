import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Box, Typography } from '@mui/material';

const UserReadBarChart = ({ userData }) => {
  // Sort users by read percentage (descending)
  const sortedData = [...userData]
    .sort((a, b) => b.readPercentage - a.readPercentage)
    .slice(0, 5); // Only show top 5 users

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Box sx={{ bgcolor: 'background.paper', p: 1.5, boxShadow: 1, borderRadius: 1 }}>
          <Typography variant="body2" fontWeight="medium">{data.username}</Typography>
          <Typography variant="body2" color="text.secondary">
            Read: {data.read} ({data.readPercentage}%)
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Unread: {data.unread}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Total: {data.total}
          </Typography>
        </Box>
      );
    }
    return null;
  };

  return (
    <Box sx={{ height: 300 }}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={sortedData}
          layout="vertical"
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" horizontal={false} />
          <XAxis type="number" domain={[0, 100]} />
          <YAxis 
            type="category" 
            dataKey="username" 
            width={100}
            tick={{ fontSize: 12 }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar 
            dataKey="readPercentage" 
            name="Read Percentage" 
            fill="#3f51b5" 
            radius={[0, 4, 4, 0]}
            label={{ 
              position: 'right', 
              formatter: (value) => `${value}%`,
              fontSize: 12
            }}
          />
        </BarChart>
      </ResponsiveContainer>
    </Box>
  );
};

export default UserReadBarChart;
