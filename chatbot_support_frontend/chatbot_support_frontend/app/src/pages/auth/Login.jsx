import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import APIErrorAlert from '../../components/common/APIErrorAlert';
import { API_BASE_URL } from '../../services/api';

const Login = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [isLoginForm, setIsLoginForm] = useState(true);
  const [showAPIError, setShowAPIError] = useState(false);

  const { login, register, isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Get the redirect path from location state or determine based on user role
  const getRedirectPath = () => {
    // If there's a specific path in the location state, use that
    if (location.state?.from?.pathname) {
      console.log('Login - Redirecting to saved location:', location.state.from.pathname);
      return location.state.from.pathname;
    }

    // Otherwise, redirect based on user role
    const defaultPath = user?.role?.toLowerCase() === 'admin' ? '/admin/dashboard' : '/chat';
    console.log('Login - Redirecting to default path based on role:', defaultPath);
    return defaultPath;
  };

  // Check for dark mode preference
  useEffect(() => {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      setDarkMode(true);
    }

    // Check for saved preference
    const savedMode = localStorage.getItem('darkMode');
    if (savedMode) {
      setDarkMode(savedMode === 'true');
    }
  }, []);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      console.log('Login - Already authenticated, redirecting');
      const redirectPath = getRedirectPath();
      console.log('Login - Redirect path:', redirectPath);
      navigate(redirectPath);
    }
  }, [isAuthenticated, navigate, user, location.state]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!username || !password) {
      setError('Please enter both username and password');
      return;
    }

    try {
      setLoading(true);
      setError('');

      if (isLoginForm) {
        // Handle login
        const result = await login(username, password);

        if (!result.success) {
          setError(result.error || 'Failed to login');
        } else {
          // Debug the login response
          console.log('Login response:', result);

          // Redirect based on saved location or user role
          console.log('Login successful, redirecting');
          const redirectPath = getRedirectPath();
          console.log('Redirect path after login:', redirectPath);
          navigate(redirectPath);
        }
      } else {
        // Handle registration
        if (password !== confirmPassword) {
          setError('Passwords do not match');
          setLoading(false);
          return;
        }

        const result = await register(username, password);

        if (!result.success) {
          setError(result.error || 'Failed to register');
        } else {
          // New users are typically regular users, so redirect to chat
          navigate('/chat');
        }
      }
    } catch (err) {
      console.error('Login/Register error:', err);

      // Check if this is an API connection error
      if (err.message && (
        err.message.includes('Failed to fetch') ||
        err.message.includes('Network Error') ||
        err.message.includes('Server returned HTML instead of JSON')
      )) {
        // Show the API error alert
        setShowAPIError(true);
        setError('Cannot connect to the server. Please check your connection.');
      } else {
        // Show regular error
        setError(err.message || 'An unexpected error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  // Function to retry the connection
  const handleRetryConnection = () => {
    setShowAPIError(false);
    setError('');
    // We don't need to do anything else, just clear the error state
  };

  // Function to dismiss the API error
  const handleDismissAPIError = () => {
    setShowAPIError(false);
  };

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: darkMode ? '#111827' : '#F3F4F6'
    }}>
      {/* API Error Alert */}
      {showAPIError && (
        <APIErrorAlert
          message="Cannot connect to the server. Please check your connection or try again later."
          apiUrl={API_BASE_URL}
          onRetry={handleRetryConnection}
          onDismiss={handleDismissAPIError}
        />
      )}
      <div style={{
        maxWidth: '28rem',
        width: '100%',
        padding: '2.5rem',
        borderRadius: '0.75rem',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        backgroundColor: darkMode ? '#1F2937' : '#FFFFFF'
      }}>
        <div>
          <h2 style={{
            marginTop: '1.5rem',
            textAlign: 'center',
            fontSize: '1.875rem',
            fontWeight: '800',
            color: darkMode ? '#F9FAFB' : '#111827'
          }}>
            {isLoginForm ? 'Sign in to your account' : 'Create a new account'}
          </h2>
          <p style={{
            marginTop: '0.5rem',
            textAlign: 'center',
            fontSize: '0.875rem',
            color: darkMode ? '#9CA3AF' : '#6B7280'
          }}>
            {isLoginForm ? 'Access the RAG Chatbot dashboard' : 'Join the RAG Chatbot platform'}
          </p>
        </div>

        <form style={{ marginTop: '2rem', display: 'flex', flexDirection: 'column', gap: '1.5rem' }} onSubmit={handleSubmit}>
          {error && (
            <div style={{
              padding: '1rem',
              borderRadius: '0.375rem',
              backgroundColor: '#FEE2E2',
              color: '#B91C1C'
            }}>
              {error}
            </div>
          )}

          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            <div>
              <label htmlFor="username" style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: darkMode ? '#D1D5DB' : '#374151'
              }}>
                Username
              </label>
              <input
                id="username"
                name="username"
                type="text"
                autoComplete="username"
                required
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                style={{
                  display: 'block',
                  width: '100%',
                  padding: '0.625rem',
                  borderRadius: '0.375rem',
                  backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                  borderWidth: '1px',
                  borderStyle: 'solid',
                  borderColor: darkMode ? '#4B5563' : '#D1D5DB',
                  color: darkMode ? '#F9FAFB' : '#111827',
                  outline: 'none'
                }}
                placeholder="Enter your username"
              />
            </div>

            <div>
              <label htmlFor="password" style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: darkMode ? '#D1D5DB' : '#374151'
              }}>
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete={isLoginForm ? "current-password" : "new-password"}
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                style={{
                  display: 'block',
                  width: '100%',
                  padding: '0.625rem',
                  borderRadius: '0.375rem',
                  backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                  borderWidth: '1px',
                  borderStyle: 'solid',
                  borderColor: darkMode ? '#4B5563' : '#D1D5DB',
                  color: darkMode ? '#F9FAFB' : '#111827',
                  outline: 'none'
                }}
                placeholder={isLoginForm ? "Enter your password" : "Create a password"}
              />
            </div>

            {!isLoginForm && (
              <div>
                <label htmlFor="confirmPassword" style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: darkMode ? '#D1D5DB' : '#374151'
                }}>
                  Confirm Password
                </label>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  style={{
                    display: 'block',
                    width: '100%',
                    padding: '0.625rem',
                    borderRadius: '0.375rem',
                    backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                    borderWidth: '1px',
                    borderStyle: 'solid',
                    borderColor: darkMode ? '#4B5563' : '#D1D5DB',
                    color: darkMode ? '#F9FAFB' : '#111827',
                    outline: 'none'
                  }}
                  placeholder="Confirm your password"
                />
              </div>
            )}
          </div>

          {isLoginForm && (
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  style={{
                    height: '1rem',
                    width: '1rem',
                    borderRadius: '0.25rem',
                    backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                    borderWidth: '1px',
                    borderStyle: 'solid',
                    borderColor: darkMode ? '#4B5563' : '#D1D5DB'
                  }}
                />
                <label htmlFor="remember-me" style={{
                  marginLeft: '0.5rem',
                  fontSize: '0.875rem',
                  color: darkMode ? '#D1D5DB' : '#374151'
                }}>
                  Remember me
                </label>
              </div>

              <div>
                <button
                  type="button"
                  style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: darkMode ? '#60A5FA' : '#2563EB',
                    backgroundColor: 'transparent',
                    border: 'none',
                    cursor: 'pointer'
                  }}
                >
                  Forgot password?
                </button>
              </div>
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={loading}
              style={{
                display: 'flex',
                width: '100%',
                justifyContent: 'center',
                alignItems: 'center',
                padding: '0.625rem 1.25rem',
                borderRadius: '0.375rem',
                backgroundColor: darkMode ? '#2563EB' : '#3B82F6',
                color: '#FFFFFF',
                fontWeight: '500',
                fontSize: '0.875rem',
                border: 'none',
                cursor: loading ? 'not-allowed' : 'pointer',
                opacity: loading ? 0.7 : 1
              }}
            >
              {loading
                ? (isLoginForm ? 'Signing in...' : 'Creating account...')
                : (isLoginForm ? 'Sign in' : 'Create account')}
            </button>
          </div>

          <div style={{
            marginTop: '1rem',
            textAlign: 'center',
            fontSize: '0.875rem',
            color: darkMode ? '#9CA3AF' : '#6B7280'
          }}>
            {isLoginForm ? "Don't have an account?" : "Already have an account?"}{' '}
            <button
              type="button"
              onClick={() => {
                setIsLoginForm(!isLoginForm);
                setError('');
              }}
              style={{
                fontWeight: '500',
                color: darkMode ? '#60A5FA' : '#2563EB',
                backgroundColor: 'transparent',
                border: 'none',
                cursor: 'pointer'
              }}
            >
              {isLoginForm ? 'Sign up' : 'Sign in'}
            </button>
          </div>

          {isLoginForm && (
            <div style={{
              marginTop: '1rem',
              textAlign: 'center',
              fontSize: '0.75rem',
              color: darkMode ? '#6B7280' : '#9CA3AF'
            }}>
              <p>JMSC POS</p>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};

export default Login;
