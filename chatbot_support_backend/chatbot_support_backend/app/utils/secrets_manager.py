import os
import boto3
import json

def get_db_credentials():
    if os.getenv('ENV', 'local') == 'local':
        return {
            "host": os.getenv("DB_HOST", "db"),
            "port": int(os.getenv("DB_PORT", "3306")),
            "user": os.getenv("DB_USER", "raguser"),
            "password": os.getenv("DB_PASSWORD", "ragpassword"),
            "database": os.getenv("DB_NAME", "ragchatbot")
        }
    else:
        secret_arn = os.getenv("DB_SECRET_ARN")
        secret1 = get_secret(secret_arn)
        secret2 = get_secret(secret1.get("creds_secret_manager_arn"))
        
        return {
            "host": secret1["server"],
            "port": int(secret1["port"]),
            "user": secret2["username"],
            "password": secret2["password"],
            "database": os.getenv("DB_NAME")
        }

def get_secret(secret_arn: str):
    if os.getenv('ENV', 'local') == 'local':
        client = boto3.client(
            service_name='secretsmanager',
            aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
            aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
            region_name=os.getenv("AWS_REGION", "us-east-1")
        )
    else:
        client = boto3.client(
            service_name='secretsmanager',
            region_name=os.getenv("AWS_REGION", "us-east-1")
        )
    
    try:
        get_secret_value_response = client.get_secret_value(SecretId=secret_arn)
        secret_string = get_secret_value_response['SecretString']
        return json.loads(secret_string)
    except Exception as e:
        print(f"Error retrieving secret: {e}")
        raise e
