version: '3.8'

services:
  # Backend service
  backend:
    build:
      context: ./chatbot_support_backend
      dockerfile: Dockerfile
    restart: always
    ports:
      - "8000:8000"
    environment:
      # Environment setting - uses value from .env file
      - ENV=${ENV:-production}

      # Database configuration
      - DB_HOST=${DB_HOST:-db}
      - DB_PORT=${DB_PORT:-3306}
      - DB_USER=${DB_USER:-root}
      - DB_PASSWORD=${DB_PASSWORD:-rootpassword}
      - DB_NAME=${DB_NAME:-ragchatbot}
      - DATABASE_LOGGING=${DATABASE_LOGGING:-true}

      # AWS S3 configuration
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - S3_BUCKET_NAME=${S3_BUCKET_NAME}
      - S3_PREFIX=${S3_PREFIX:-chatbot_support}
      - USE_REAL_S3=${USE_REAL_S3:-false}
      - DB_SECRET_ARN=${DB_SECRET_ARN}

      # Vector store configuration
      - VECTOR_STORE_TYPE=${VECTOR_STORE_TYPE:-chroma}
      - VECTOR_STORE_DIR=${VECTOR_STORE_DIR:-/app/central_vector_store}
      - USER_DATA_DIR=${USER_DATA_DIR:-/app/user_data}

      # External ChromaDB configuration
      - USE_EXTERNAL_CHROMA=${USE_EXTERNAL_CHROMA:-false}
      - CHROMA_HOST=${CHROMA_HOST:-localhost}
      - CHROMA_PORT=${CHROMA_PORT:-8000}
      - CHROMA_COLLECTION_NAME=${CHROMA_COLLECTION_NAME:-support_chatbot}

      # JWT configuration
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-secret-key-change-in-production}
      - JWT_ALGORITHM=${JWT_ALGORITHM:-HS256}
      - JWT_ACCESS_TOKEN_EXPIRE_MINUTES=${JWT_ACCESS_TOKEN_EXPIRE_MINUTES:-30}

      # Google AI configuration
      - GOOGLE_SERVICE_ACCOUNT_FILE=${GOOGLE_SERVICE_ACCOUNT_FILE:-/app/google_credentials.json}
      - GOOGLE_PROJECT_ID=${GOOGLE_PROJECT_ID}
      - GOOGLE_CREDENTIALS_SECRET_NAME=${GOOGLE_CREDENTIALS_SECRET_NAME:-google-service-account-credentials}

      # CORS configuration
      - CORS_ORIGINS=${CORS_ORIGINS:-*}

      # Logging and debugging
      - PYTHONUNBUFFERED=${PYTHONUNBUFFERED:-1}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      # Google credentials file
      - ./google_credentials.json:/app/google_credentials.json:ro
      - data-volume:/app/data
      - vector-store-volume:/app/central_vector_store
      - user-data-volume:/app/user_data
    depends_on:
      db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/health')"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 60s
    networks:
      - chatbot_support_backend_chatbot-network

  # Database service
  db:
    image: mysql:8.0
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD:-rootpassword}
      - MYSQL_DATABASE=${DB_NAME:-ragchatbot}
    volumes:
      - mysql-data:/var/lib/mysql
      - ./init_scripts/db:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-prootpassword"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - chatbot_support_backend_chatbot-network

  # ChromaDB service for external vector storage
  chromadb:
    image: chromadb/chroma:latest
    restart: always
    ports:
      - "8001:8000"  # Map to port 8001 to avoid conflict with backend's 8000
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
      - IS_PERSISTENT=TRUE
      - PERSIST_DIRECTORY=/chroma/chroma
      - ANONYMIZED_TELEMETRY=FALSE
    volumes:
      - chromadb-data:/chroma/chroma
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - chatbot_support_backend_chatbot-network # Use the same network as backend and db

volumes:
  data-volume:
    driver: local
  user-data-volume:
    driver: local
  vector-store-volume:
    driver: local
  mysql-data:
    driver: local
  chromadb-data: # Add chromadb-data volume
    driver: local

networks:
  chatbot_support_backend_chatbot-network:
    driver: bridge
