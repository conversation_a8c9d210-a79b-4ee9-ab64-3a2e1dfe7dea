import React, { useEffect, useState } from 'react';
import { marked } from 'marked'; // Import marked directly
import hljs from 'highlight.js'; // Import highlight.js directly
import 'highlight.js/styles/github.css'; // Or any other theme you prefer
import './MarkdownRenderer.css';

/**
 * A component to render markdown content as HTML using the marked library
 *
 * @param {Object} props
 * @param {string} props.content - The markdown content to render
 * @param {Object} props.style - Additional styles to apply to the container
 */
const MarkdownRenderer = ({ content, style = {} }) => {
  const [html, setHtml] = useState('');

  // Apply syntax highlighting to code blocks after rendering
  useEffect(() => {
    if (html) {
      // Find all code blocks and apply syntax highlighting
      const codeBlocks = document.querySelectorAll('.markdown-content pre code');
      codeBlocks.forEach(block => {
        hljs.highlightElement(block);
      });
    }
  }, [html]);

  useEffect(() => {
    // Function to convert markdown to HTML using marked
    const renderMarkdown = () => {
      if (!content) {
        setHtml('');
        return;
      }

      try {
        // Configure marked options
        marked.setOptions({
          breaks: true,        // Add line breaks when \n is encountered
          gfm: true,           // Use GitHub Flavored Markdown
          headerIds: true,     // Add IDs to headers
          mangle: false,       // Don't mangle header IDs
          sanitize: false,     // Don't sanitize HTML (React will handle this)
          smartLists: true,    // Use smarter list behavior
          smartypants: true,   // Use "smart" typographic punctuation
          xhtml: false,        // Don't use self-closing tags
          highlight: function(code, lang) {
            // Use highlight.js for code syntax highlighting
            if (lang && hljs.getLanguage(lang)) {
              try {
                return hljs.highlight(code, { language: lang }).value;
              } catch (err) {
                console.error('Error highlighting code:', err);
              }
            }
            return code; // Return original code if highlighting fails
          }
        });

        // Convert markdown to HTML
        const renderedHtml = marked.parse(content);
        setHtml(renderedHtml);
      } catch (error) {
        console.error('Error rendering markdown:', error);
        // Fallback to plain text if rendering fails
        setHtml(`<p>${content}</p>`);
      }
    };

    renderMarkdown();
  }, [content]);

  return (
    <div
      style={{
        fontSize: '0.875rem',
        lineHeight: '1.5',
        ...style
      }}
      dangerouslySetInnerHTML={{ __html: html }}
      className="markdown-content"
    />
  );
};

export default MarkdownRenderer;
