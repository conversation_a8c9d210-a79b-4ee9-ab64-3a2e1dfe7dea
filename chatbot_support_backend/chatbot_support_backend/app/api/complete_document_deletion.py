"""
Complete Document Deletion Module

This module provides functions for completely deleting documents from all storage locations:
1. Database (document records, embeddings, access records)
2. Vector stores (Chroma, FAISS, etc.)
3. S3 storage
4. Local file system

It also ensures that all chatbots are reinitialized after deletion to provide live updates.
"""

import os
import shutil
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

from app.database.db import get_db
from app.database.models import Document as DBDocument
from app.database.models import Embedding as DBEmbedding
from app.database.models import DocumentAccess
from app.storage.s3 import S3Storage
from app.storage.chatbot_storage import ChatbotStorage

# Set up logging
logger = logging.getLogger(__name__)

def completely_delete_document(
    document_id: str,
    s3_storage: S3Storage,
    chatbot_storage: ChatbotStorage,
    user_data_dir: Path,
    data_dir: Path = Path("data")
) -> Dict[str, Any]:
    """
    Completely delete a document from all storage locations.

    This function:
    1. Deletes the document from the database
    2. Deletes all embeddings associated with the document
    3. Deletes all document access records
    4. Deletes the document from S3
    5. Deletes the document from all vector stores
    6. Deletes any local copies of the document
    7. Forces reinitialization of all chatbots

    Args:
        document_id: The ID of the document to delete
        s3_storage: S3Storage instance
        chatbot_storage: ChatbotStorage instance
        user_data_dir: Path to the user data directory
        data_dir: Path to the common data directory

    Returns:
        Dictionary with deletion results
    """
    logger.info(f"Starting complete deletion of document {document_id}")
    
    results = {
        "document_id": document_id,
        "database_deletion": False,
        "embeddings_deleted": 0,
        "access_records_deleted": 0,
        "s3_deletion": False,
        "vector_store_deletions": {},
        "local_file_deletions": [],
        "chatbot_reinitializations": {},
        "errors": []
    }
    
    try:
        # Step 1: Get document details from database
        with get_db() as db:
            document = db.query(DBDocument).filter(DBDocument.id == document_id).first()
            
            if not document:
                error_msg = f"Document with ID {document_id} not found in database"
                logger.warning(error_msg)
                results["errors"].append(error_msg)
                return results
            
            # Store document details for later use
            s3_key = document.s3_key
            filename = document.filename
            results["filename"] = filename
            results["s3_key"] = s3_key
            
            # Step 2: Delete embeddings
            embedding_count = db.query(DBEmbedding).filter(DBEmbedding.document_id == document_id).count()
            db.query(DBEmbedding).filter(DBEmbedding.document_id == document_id).delete()
            results["embeddings_deleted"] = embedding_count
            logger.info(f"Deleted {embedding_count} embeddings for document {document_id}")
            
            # Step 3: Delete document access records
            access_count = db.query(DocumentAccess).filter(DocumentAccess.document_id == document_id).count()
            db.query(DocumentAccess).filter(DocumentAccess.document_id == document_id).delete()
            results["access_records_deleted"] = access_count
            logger.info(f"Deleted {access_count} access records for document {document_id}")
            
            # Step 4: Delete the document from the database
            db.delete(document)
            db.commit()
            results["database_deletion"] = True
            logger.info(f"Deleted document {document_id} from database")
        
        # Step 5: Delete the file from S3
        if s3_key and not s3_key.startswith('local/'):
            try:
                s3_storage.delete_file(s3_key)
                results["s3_deletion"] = True
                logger.info(f"Deleted file {s3_key} from S3")
            except Exception as e:
                error_msg = f"Error deleting file from S3: {str(e)}"
                logger.error(error_msg)
                results["errors"].append(error_msg)
        
        # Step 6: Delete the document from all vector stores
        try:
            # Get all chatbots from storage
            all_chatbots = chatbot_storage.get_all_chatbots()
            logger.info(f"Found {len(all_chatbots)} chatbots in memory to update")
            
            # Delete document from each chatbot's vector store
            for user_id, chatbot in all_chatbots.items():
                try:
                    if chatbot and chatbot.vector_store:
                        # First try the standard delete_document method
                        success = chatbot.delete_document(document_id)
                        
                        # If that fails, try a more aggressive approach
                        if not success and chatbot.vector_store.store_type == "chroma":
                            try:
                                # Try to delete by metadata filter with different approaches
                                if hasattr(chatbot.vector_store.vector_store, 'delete'):
                                    # Try different metadata fields that might contain the document ID
                                    for metadata_field in ["document_id", "source", "doc_id"]:
                                        try:
                                            chatbot.vector_store.vector_store.delete(
                                                where={metadata_field: document_id}
                                            )
                                            logger.info(f"Deleted document {document_id} from user {user_id}'s Chroma vector store using {metadata_field} filter")
                                            success = True
                                        except Exception as e:
                                            logger.warning(f"Error deleting document using {metadata_field} filter: {str(e)}")
                                    
                                    # If all specific filters failed, try a more general approach
                                    if not success:
                                        try:
                                            # Try to find any chunks that might contain the document ID or filename in their metadata
                                            results_with_doc_id = chatbot.vector_store.vector_store.get(
                                                where={"document_id": document_id}
                                            )
                                            if results_with_doc_id and len(results_with_doc_id['ids']) > 0:
                                                chatbot.vector_store.vector_store.delete(
                                                    ids=results_with_doc_id['ids']
                                                )
                                                logger.info(f"Deleted {len(results_with_doc_id['ids'])} chunks with document_id={document_id} from user {user_id}'s Chroma vector store")
                                                success = True
                                        except Exception as e:
                                            logger.warning(f"Error deleting document chunks by ID: {str(e)}")
                                            
                                    # If all else fails, try to reset the vector store
                                    if not success:
                                        logger.warning(f"Could not delete document {document_id} from user {user_id}'s vector store using standard methods, attempting reset")
                                        success = chatbot.reset_vector_store()
                                        if success:
                                            logger.info(f"Reset vector store for user {user_id} after failing to delete document {document_id}")
                            except Exception as e:
                                logger.error(f"Error with aggressive deletion for user {user_id}: {str(e)}")
                        
                        # Save the vector store to persist changes
                        if success:
                            chatbot.save_vector_store()
                            
                            # Reinitialize the retriever
                            if chatbot.retriever:
                                chatbot.retriever = None
                                chatbot.initialize_retriever()
                                logger.info(f"Reinitialized retriever for user {user_id}")
                            
                            # Clear conversation memory
                            chatbot.clear_conversation()
                            logger.info(f"Cleared conversation memory for user {user_id}")
                            
                            # Update the chatbot in storage
                            chatbot_storage.update(user_id, chatbot)
                            logger.info(f"Updated chatbot for user {user_id} in storage")
                        
                        results["vector_store_deletions"][user_id] = success
                    else:
                        results["vector_store_deletions"][user_id] = False
                        logger.warning(f"Chatbot or vector store for user {user_id} is None, skipping vector store deletion")
                except Exception as e:
                    error_msg = f"Error deleting document from vector store for user {user_id}: {str(e)}"
                    logger.error(error_msg)
                    results["errors"].append(error_msg)
                    results["vector_store_deletions"][user_id] = False
        except Exception as e:
            error_msg = f"Error getting chatbots from storage: {str(e)}"
            logger.error(error_msg)
            results["errors"].append(error_msg)
        
        # Step 7: Delete any local copies of the document
        try:
            # Check common data directory
            if data_dir.exists():
                common_file = data_dir / filename
                if common_file.exists():
                    try:
                        os.remove(common_file)
                        results["local_file_deletions"].append(str(common_file))
                        logger.info(f"Deleted local file {common_file}")
                    except Exception as e:
                        error_msg = f"Error deleting local file {common_file}: {str(e)}"
                        logger.error(error_msg)
                        results["errors"].append(error_msg)
            
            # Check user data directories
            if user_data_dir.exists():
                for user_dir in user_data_dir.iterdir():
                    if user_dir.is_dir():
                        user_data_file = user_dir / "data" / filename
                        if user_data_file.exists():
                            try:
                                os.remove(user_data_file)
                                results["local_file_deletions"].append(str(user_data_file))
                                logger.info(f"Deleted local file {user_data_file}")
                            except Exception as e:
                                error_msg = f"Error deleting local file {user_data_file}: {str(e)}"
                                logger.error(error_msg)
                                results["errors"].append(error_msg)
        except Exception as e:
            error_msg = f"Error deleting local files: {str(e)}"
            logger.error(error_msg)
            results["errors"].append(error_msg)
        
        # Step 8: Force reinitialization of all chatbots
        try:
            # Clear all chatbots from memory
            chatbot_storage.clear_all()
            logger.info("Cleared all chatbot instances from memory to force reinitialization")
            results["chatbots_cleared"] = True
        except Exception as e:
            error_msg = f"Error clearing chatbot instances: {str(e)}"
            logger.error(error_msg)
            results["errors"].append(error_msg)
            results["chatbots_cleared"] = False
        
        # Calculate success statistics
        results["success"] = (
            results["database_deletion"] and
            (not s3_key or results["s3_deletion"]) and
            any(results["vector_store_deletions"].values()) and
            results.get("chatbots_cleared", False)
        )
        
        logger.info(f"Complete deletion of document {document_id} finished with success={results['success']}")
        return results
    
    except Exception as e:
        error_msg = f"Unexpected error during complete document deletion: {str(e)}"
        logger.error(error_msg)
        results["errors"].append(error_msg)
        results["success"] = False
        return results
