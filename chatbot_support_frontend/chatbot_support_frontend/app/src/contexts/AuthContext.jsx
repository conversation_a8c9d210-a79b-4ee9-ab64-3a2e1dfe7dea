import React, { createContext, useState, useContext, useEffect } from 'react';
import { authAPI, fetchWithAuth, userAPI, API_BASE_URL } from '../services/api';
import { sessionAPI } from '../services/sessionAPI';
import { getCookie, deleteCookie } from '../utils/cookies';

// Create auth context
const AuthContext = createContext();

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  // Function to check if the user is authenticated via cookies
  const checkUserAuthFromCookies = () => {
    try {
      // Try to get user data from the non-HttpOnly cookie
      const userDataCookie = getCookie('user_data');
      if (userDataCookie) {
        try {
          const userData = JSON.parse(userDataCookie);
          if (userData && userData.id && userData.username) {
            console.log('Found user data in cookie:', userData);
            setUser(userData);
            setIsAuthenticated(true);
            return true;
          }
        } catch (e) {
          console.log('Error parsing user data cookie, but continuing:', e);
        }
      }

      // If no user_data cookie, check for other cookies that might indicate authentication
      const allCookies = document.cookie;
      if (allCookies.includes('access_token') || allCookies.includes('refresh_token')) {
        console.log('Found authentication cookies, but no user data. Will try to refresh session.');
        // We found auth cookies but no user data, we'll let the SessionPersistence component handle this
        return false;
      }

      return false;
    } catch (error) {
      console.log('Error checking user auth from cookies, but continuing:', error);
      return false;
    }
  };

  // Fetch user's groups
  const fetchUserGroups = async (userId) => {
    try {
      // First try to get user details from /users/me/details endpoint
      try {
        const response = await fetch(`${API_BASE_URL}/users/me/details`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          if (data && data.user && data.user.groups) {
            console.log('Fetched user details with groups from /users/me/details:', data.user);
            return data.user.groups || [];
          }
        }
      } catch (meDetailsError) {
        console.error('Error fetching from /users/me/details, trying fallback:', meDetailsError);
      }

      // Fallback to getUserById
      const response = await userAPI.getUserById(userId);
      if (response && response.user) {
        console.log('Fetched user details with groups from getUserById:', response.user);
        return response.user.groups || [];
      }

      // If both methods fail, try localStorage
      return [];
    } catch (error) {
      console.error('Error fetching user groups:', error);

      // If we can't fetch groups from the API, try to get them from localStorage as fallback
      try {
        const userData = localStorage.getItem('userData');
        if (userData) {
          const parsedUserData = JSON.parse(userData);
          if (parsedUserData.groups && Array.isArray(parsedUserData.groups)) {
            console.log('Using groups from localStorage:', parsedUserData.groups);
            return parsedUserData.groups;
          }
        }
      } catch (localStorageError) {
        console.error('Error getting groups from localStorage:', localStorageError);
      }

      return [];
    }
  };

  // Update user data with groups
  const updateUserWithGroups = async (userData) => {
    if (!userData || !userData.id) return userData;

    try {
      const groups = await fetchUserGroups(userData.id);
      console.log('Fetched groups for user:', groups);
      return { ...userData, groups };
    } catch (error) {
      console.error('Error updating user with groups:', error);
      return userData;
    }
  };

  // Check if user is already logged in on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        console.log('Checking authentication status...');

        // First, try to get authentication from cookies
        const cookieAuth = checkUserAuthFromCookies();
        if (cookieAuth) {
          console.log('User authenticated via cookies');

          // If we have a user from cookies, fetch their groups
          if (user && user.id) {
            const updatedUser = await updateUserWithGroups(user);
            setUser(updatedUser);
          }

          setLoading(false);
          return;
        }

        // If not authenticated via cookies, try the localStorage method
        const token = localStorage.getItem('authToken');
        const userData = localStorage.getItem('userData');

        console.log('CheckAuth - Token exists:', !!token);
        console.log('CheckAuth - UserData exists:', !!userData);

        if (token && userData) {
          try {
            // Parse user data
            const parsedUserData = JSON.parse(userData);
            console.log('CheckAuth - Parsed user data:', parsedUserData);

            try {
              // Validate token by making a request to the API
              const response = await fetchWithAuth(`${API_BASE_URL}/users/me`, {
                method: 'GET',
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json',
                },
              });

              if (response.ok) {
                // Token is valid, set user and authentication state
                console.log('CheckAuth - Token is valid, setting user:', parsedUserData);

                // Fetch user's groups
                const updatedUser = await updateUserWithGroups(parsedUserData);
                setUser(updatedUser);
                setIsAuthenticated(true);
              } else {
                // Token is invalid, clear storage
                console.log('Invalid token detected, clearing auth data');
                clearAuthData();
              }
            } catch (validationError) {
              console.log('Token validation error, but continuing:', validationError);
              // Don't clear token on network errors to allow offline usage
              // Still set the user as authenticated based on local storage
              console.log('CheckAuth - Using cached user data due to network error:', parsedUserData);

              // Try to fetch groups but don't fail if it doesn't work
              try {
                const updatedUser = await updateUserWithGroups(parsedUserData);
                setUser(updatedUser);
              } catch (groupError) {
                console.error('Error fetching groups in offline mode:', groupError);
                setUser(parsedUserData);
              }

              setIsAuthenticated(true);
            }
          } catch (parseError) {
            console.log('Error parsing user data from localStorage:', parseError);
            clearAuthData();
          }
        }
      } catch (error) {
        console.log('Authentication error, clearing auth data:', error);
        clearAuthData();
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Login function
  const login = async (username, password) => {
    try {
      // Call the login API with credentials
      const response = await authAPI.login(username, password);
      console.log('Login API response:', response);

      if (response && response.access_token) {
        // For backward compatibility, still store in localStorage
        localStorage.setItem('authToken', response.access_token);
        if (response.refresh_token) {
          localStorage.setItem('refreshToken', response.refresh_token);
        }

        // Create user object
        const userData = {
          id: response.user_id,
          username: response.username,
          role: response.role
        };

        console.log('User data to be stored:', userData);

        // Fetch user's groups
        const updatedUser = await updateUserWithGroups(userData);
        console.log('User data with groups:', updatedUser);

        // Store user data in localStorage for backward compatibility
        localStorage.setItem('userData', JSON.stringify(updatedUser));

        // Update state
        setUser(updatedUser);
        setIsAuthenticated(true);

        // No need to set cookies here as they are set by the server

        return {
          success: true,
          user: updatedUser
        };
      } else {
        return {
          success: false,
          error: 'Invalid response from server'
        };
      }
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        error: typeof error === 'string' ? error : 'An error occurred during login'
      };
    }
  };

  // Register function
  const register = async (username, password) => {
    try {
      // Call the register API with credentials
      const response = await authAPI.register(username, password);

      if (response && response.access_token) {
        // For backward compatibility, still store in localStorage
        localStorage.setItem('authToken', response.access_token);
        if (response.refresh_token) {
          localStorage.setItem('refreshToken', response.refresh_token);
        }

        // Create user object
        const userData = {
          id: response.user_id,
          username: response.username,
          role: response.role
        };

        // New users typically don't have groups yet, but try to fetch them anyway
        const updatedUser = await updateUserWithGroups(userData);

        // Store user data in localStorage for backward compatibility
        localStorage.setItem('userData', JSON.stringify(updatedUser));

        // Update state
        setUser(updatedUser);
        setIsAuthenticated(true);

        // No need to set cookies here as they are set by the server

        return { success: true };
      } else {
        return {
          success: false,
          error: 'Invalid response from server'
        };
      }
    } catch (error) {
      console.error('Registration error:', error);
      return {
        success: false,
        error: typeof error === 'string' ? error : 'An error occurred during registration'
      };
    }
  };

  // Clear all auth-related data
  const clearAuthData = () => {
    try {
      // Clear localStorage
      localStorage.removeItem('authToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('userData');

      // Clear cookies (non-HttpOnly ones that we can access)
      try {
        deleteCookie('user_data', { path: '/' });
        // Try to clear other potential cookies
        deleteCookie('access_token', { path: '/' });
        deleteCookie('refresh_token', { path: '/' });
      } catch (cookieError) {
        console.log('Error clearing cookies, but continuing:', cookieError);
      }
    } catch (error) {
      console.log('Error clearing auth data, but continuing:', error);
    } finally {
      // Always reset state
      setUser(null);
      setIsAuthenticated(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Call the logout API to invalidate the session and clear cookies
      await sessionAPI.logout();
    } catch (error) {
      console.error('Error calling logout API:', error);
    } finally {
      // Clear all auth-related data
      clearAuthData();
    }
  };

  // Context value
  const value = {
    user,
    isAuthenticated,
    loading,
    login,
    register,
    logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
