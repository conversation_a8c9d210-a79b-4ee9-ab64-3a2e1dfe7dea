import React, { useState, useEffect } from 'react';
import { useNavigate, NavLink, useLocation } from 'react-router-dom';
import { Outlet } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  ArrowRightStartOnRectangleIcon,
  UserCircleIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import NotificationBell from '../components/notifications/NotificationBell';

const UserLayout = () => {
  const [darkMode, setDarkMode] = useState(false);
  const [profileOpen, setProfileOpen] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const { logout, user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Check for user's preferred color scheme
  useEffect(() => {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      setDarkMode(true);
      document.body.classList.add('dark');
    }

    // Check for saved preference
    const savedMode = localStorage.getItem('darkMode');
    if (savedMode) {
      const isDarkMode = savedMode === 'true';
      setDarkMode(isDarkMode);
      if (isDarkMode) {
        document.body.classList.add('dark');
      } else {
        document.body.classList.remove('dark');
      }
    }
  }, []);

  // Handle logout
  const handleLogout = () => {
    logout();
    navigate('/login');
    console.log('Logged out successfully');
  };

  return (
    <div className="user-container" style={{
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      backgroundColor: darkMode ? '#111827' : '#F9FAFB'
    }}>
      {/* Simple header with user info and logout */}
      <header style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '1rem',
        backgroundColor: darkMode ? '#1F2937' : '#FFFFFF',
        borderBottom: `1px solid ${darkMode ? '#374151' : '#E5E7EB'}`,
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <img
            src="/logo.png"
            alt="Company Logo"
            style={{ height: '3.5rem', width: '12rem', marginRight: '0.75rem' }}
          />
          <h1 style={{
            fontSize: '1.25rem',
            fontWeight: 600,
            color: darkMode ? '#F9FAFB' : '#111827'
          }}>Document AI Chat</h1>
        </div>

        {/* User profile */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          {/* Notification Bell */}
          <NotificationBell darkMode={darkMode} />

          {/* User Profile */}
          <div style={{ position: 'relative' }}>
            <button
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                background: 'none',
                border: 'none',
                cursor: 'pointer'
              }}
              onClick={() => setProfileOpen(!profileOpen)}
            >
            <div style={{
              height: '2rem',
              width: '2rem',
              borderRadius: '9999px',
              backgroundColor: darkMode ? '#374151' : '#E5E7EB',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <UserCircleIcon style={{
                height: '1.75rem',
                width: '1.75rem',
                color: darkMode ? '#9CA3AF' : '#6B7280'
              }} />
            </div>
            <span style={{
              fontSize: '0.875rem',
              fontWeight: 500,
              color: darkMode ? '#F9FAFB' : '#111827'
            }}>{user?.username || 'User'}</span>
          </button>

          {/* Profile dropdown */}
          {profileOpen && (
            <div style={{
              position: 'absolute',
              right: 0,
              marginTop: '0.5rem',
              width: '12rem',
              borderRadius: '0.375rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
              backgroundColor: darkMode ? '#1F2937' : '#FFFFFF',
              border: `1px solid ${darkMode ? '#374151' : '#E5E7EB'}`,
              zIndex: 50
            }}>
              <div style={{
                padding: '0.75rem 1rem',
                borderBottom: `1px solid ${darkMode ? '#374151' : '#E5E7EB'}`
              }}>
                <p style={{ fontSize: '0.875rem', fontWeight: 500, color: darkMode ? '#F9FAFB' : '#111827' }}>
                  {user?.username || 'User'}
                </p>
                <p style={{
                  fontSize: '0.75rem',
                  color: darkMode ? '#9CA3AF' : '#6B7280',
                  marginTop: '0.25rem'
                }}>{user?.role || 'user'}</p>
              </div>
              <button
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '100%',
                  textAlign: 'left',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  color: darkMode ? '#F87171' : '#DC2626',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer'
                }}
                onClick={handleLogout}
              >
                <ArrowRightStartOnRectangleIcon style={{ height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                Sign out
              </button>
            </div>
          )}
          </div>
        </div>
      </header>

      {/* Main content area with sidebar */}
      <div style={{
        display: 'flex',
        flex: 1,
        overflow: 'hidden'
      }}>
        {/* Sidebar */}
        <aside style={{
          width: sidebarOpen ? '250px' : '0',
          backgroundColor: darkMode ? '#1F2937' : '#FFFFFF',
          borderRight: `1px solid ${darkMode ? '#374151' : '#E5E7EB'}`,
          transition: 'width 0.3s ease',
          overflow: 'hidden'
        }}>
          <nav style={{
            padding: '1rem',
            display: 'flex',
            flexDirection: 'column',
            gap: '0.5rem'
          }}>
            <NavLink
              to="/chat"
              style={({ isActive }) => ({
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                padding: '0.75rem 1rem',
                borderRadius: '0.375rem',
                backgroundColor: isActive
                  ? (darkMode ? '#374151' : '#F3F4F6')
                  : 'transparent',
                color: isActive
                  ? (darkMode ? '#F9FAFB' : '#111827')
                  : (darkMode ? '#D1D5DB' : '#6B7280'),
                textDecoration: 'none',
                fontWeight: isActive ? '500' : 'normal'
              })}
              end
            >
              <ChatBubbleLeftRightIcon style={{ height: '1.25rem', width: '1.25rem' }} />
              <span>Chat</span>
            </NavLink>

            <NavLink
              to="/chat/documents"
              style={({ isActive }) => ({
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                padding: '0.75rem 1rem',
                borderRadius: '0.375rem',
                backgroundColor: isActive
                  ? (darkMode ? '#374151' : '#F3F4F6')
                  : 'transparent',
                color: isActive
                  ? (darkMode ? '#F9FAFB' : '#111827')
                  : (darkMode ? '#D1D5DB' : '#6B7280'),
                textDecoration: 'none',
                fontWeight: isActive ? '500' : 'normal'
              })}
            >
              <DocumentTextIcon style={{ height: '1.25rem', width: '1.25rem' }} />
              <span>Documents</span>
            </NavLink>
          </nav>
        </aside>

        {/* Main content */}
        <main style={{
          flex: 1,
          padding: '1rem',
          // overflow: 'auto'
        }}>
          <Outlet />
        </main>
      </div>

      {/* Simple footer */}
      <footer style={{
        padding: '0.5rem 1rem',
        fontSize: '0.75rem',
        color: darkMode ? '#9CA3AF' : '#6B7280',
        textAlign: 'center',
        borderTop: `1px solid ${darkMode ? '#374151' : '#E5E7EB'}`,
        backgroundColor: darkMode ? '#1F2937' : '#FFFFFF'
      }}>
        <p>© 2025 Document AI Chat</p>
      </footer>
    </div>
  );
};

export default UserLayout;
