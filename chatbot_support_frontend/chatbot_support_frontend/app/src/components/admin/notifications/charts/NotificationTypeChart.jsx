import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';
import { Box, Typography } from '@mui/material';

const NotificationTypeChart = ({ notifications }) => {
  // Count notifications by type
  const typeCount = {};
  notifications.forEach(notification => {
    const type = notification.type || 'unknown';
    typeCount[type] = (typeCount[type] || 0) + 1;
  });

  // Convert to array for chart
  const data = Object.entries(typeCount).map(([type, count]) => ({
    type: type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),
    count,
    color: getTypeColor(type)
  }));

  // Get color based on notification type
  function getTypeColor(type) {
    switch (type) {
      case 'document_added':
        return '#4caf50';
      case 'document_deleted':
        return '#f44336';
      case 'document_updated':
        return '#ff9800';
      default:
        return '#2196f3';
    }
  }

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Box sx={{ bgcolor: 'background.paper', p: 1.5, boxShadow: 1, borderRadius: 1 }}>
          <Typography variant="body2" fontWeight="medium">{data.type}</Typography>
          <Typography variant="body2" color="text.secondary">
            Count: {data.count}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Percentage: {Math.round((data.count / notifications.length) * 100)}%
          </Typography>
        </Box>
      );
    }
    return null;
  };

  return (
    <Box sx={{ height: 300 }}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis 
            dataKey="type" 
            tick={{ fontSize: 12 }}
            interval={0}
            angle={-45}
            textAnchor="end"
            height={80}
          />
          <YAxis />
          <Tooltip content={<CustomTooltip />} />
          <Bar 
            dataKey="count" 
            name="Count" 
            radius={[4, 4, 0, 0]}
            label={{ 
              position: 'top', 
              fontSize: 12
            }}
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </Box>
  );
};

export default NotificationTypeChart;
