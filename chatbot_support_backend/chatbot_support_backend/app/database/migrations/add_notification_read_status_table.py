"""
Migration script to add the NotificationReadStatus table.
"""

import uuid
from datetime import datetime
from sqlalchemy import create_engine, Column, String, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from app.config import get_database_url

# Create the engine and session
engine = create_engine(get_database_url())
Session = sessionmaker(bind=engine)
Base = declarative_base()

class NotificationReadStatus(Base):
    """Tracks which users have read which notifications."""
    __tablename__ = "notification_read_status"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    notification_id = Column(String(36), ForeignKey("notifications.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    read_at = Column(DateTime, default=datetime.now)

    # Add a unique constraint to ensure a user can only mark a notification as read once
    __table_args__ = (
        UniqueConstraint('notification_id', 'user_id', name='uix_notification_user'),
    )

def upgrade():
    """Create the notification_read_status table."""
    # Create the table
    NotificationReadStatus.__table__.create(engine, checkfirst=True)
    print("Created notification_read_status table")

def downgrade():
    """Drop the notification_read_status table."""
    # Drop the table
    NotificationReadStatus.__table__.drop(engine, checkfirst=True)
    print("Dropped notification_read_status table")

if __name__ == "__main__":
    # Run the upgrade
    upgrade()
