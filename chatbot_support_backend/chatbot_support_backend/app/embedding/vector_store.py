"""
Vector database management for storing and retrieving document embeddings.
Optimized for external ChromaDB only.
"""

import os
import logging
import uuid
import requests
from typing import List, Dict, Any, Optional, Set

from langchain.schema.document import Document
from langchain.schema.embeddings import Embeddings

logger = logging.getLogger(__name__)


def filter_metadata(metadata: Dict[str, Any]) -> Dict[str, Any]:
    """Filter complex metadata to only include simple types that ChromaDB can handle."""
    if not isinstance(metadata, dict):
        logger.warning(f"Metadata is not a dictionary: {type(metadata)}")
        return {}

    filtered = {}
    for key, value in metadata.items():
        if isinstance(value, (str, int, float, bool)):
            filtered[key] = value
        elif value is None:
            continue
        elif isinstance(value, list) and all(isinstance(item, str) for item in value):
            filtered[key] = ",".join(value)
        else:
            try:
                filtered[key] = str(value)
            except Exception as e:
                logger.warning(f"Could not convert value to string for key {key}: {str(e)}")
    return filtered


def get_chroma_config():
    """Get and validate external ChromaDB configuration."""
    if os.getenv("USE_EXTERNAL_CHROMA", "false").lower() != "true":
        raise ValueError("External ChromaDB is not enabled. Set USE_EXTERNAL_CHROMA=true")

    host = os.getenv("CHROMA_HOST")
    if not host:
        raise ValueError("CHROMA_HOST environment variable is required")

    try:
        port = int(os.getenv("CHROMA_PORT", "8000"))
    except ValueError:
        raise ValueError(f"Invalid CHROMA_PORT value. Must be a valid integer")

    return {
        "host": host,
        "port": port,
        "tenant": os.getenv("CHROMA_TENANT", "support_chatbot"),
        "database": os.getenv("CHROMA_DATABASE", "support_chatbot"),
        "collection_name": os.getenv("CHROMA_COLLECTION_NAME", "support_chatbot")
    }

class VectorStore:
    """
    Optimized vector store for external ChromaDB with all functionality consolidated.
    Provides both direct ChromaDB operations and langchain-compatible interface.
    """

    def __init__(self, embeddings: Embeddings, user_id: Optional[str] = None,
                 store_type: str = "chroma", persist_directory: Optional[str] = None, **kwargs):
        """Initialize the vector store with external ChromaDB.

        Args:
            embeddings: The embeddings instance to use
            user_id: The ID of the user who owns this vector store
            store_type: Ignored - kept for backward compatibility (always uses external ChromaDB)
            persist_directory: Ignored - kept for backward compatibility (ChromaDB is external)
            **kwargs: Additional arguments for backward compatibility
        """
        self.embeddings = embeddings
        self.user_id = user_id

        # Store for compatibility (but ignore these parameters)
        self.store_type = "chroma"  # Always external ChromaDB
        self.persist_directory = None  # Not used for external ChromaDB

        # Get ChromaDB configuration
        config = get_chroma_config()
        self.host = config["host"]
        self.port = config["port"]
        self.tenant = config["tenant"]
        self.database = config["database"]
        self.collection_name = config["collection_name"]
        self.base_url = f"http://{self.host}:{self.port}"
        self.session = requests.Session()

        # log config for debugging
        logger.info(f"ChromaDB config: {config}")
        logger.info(f"Initializing chromadb connection with host: {self.host}, port: {self.port}, tenant: {self.tenant}, database: {self.database}, collection: {self.collection_name}")

        # Test connection
        if not self._heartbeat():
            raise ConnectionError(f"Cannot connect to external ChromaDB at {self.host}:{self.port}")

        # Initialize collection and get collection ID
        self.vector_store = self  # Self-reference for compatibility
        self.collection_id = self._ensure_collection()
        if not self.collection_id:
            raise RuntimeError(f"Failed to initialize collection: {self.collection_name}")
        logger.info(f"Initialized external ChromaDB vector store with collection: {self.collection_name} (ID: {self.collection_id})")
    
        # check if collection has documents
        try:
            count_response = self.session.post(
                f"{self.base_url}/api/v2/tenants/{self.tenant}/databases/{self.database}/collections/{self.collection_id}/count"
            )
            if count_response.status_code in (200, 201):
                count = count_response.json().get("count", 0)
                logger.info(f"Collection {self.collection_name} has {count} documents")
            else:
                logger.error(f"Failed to count documents: {count_response.status_code}, {count_response.text}")
        except Exception as e:
            logger.error(f"Error counting documents: {e}")

    def _heartbeat(self) -> bool:
        """Check if the ChromaDB server is alive."""
        try:
            response = self.session.get(f"{self.base_url}/api/v2/heartbeat", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"ChromaDB heartbeat failed: {e}")
            return False

    def _ensure_collection(self) -> Optional[str]:
        """Ensure the collection exists with correct embedding dimension. Returns collection ID."""
        try:
            collection_name = self.collection_name
            tenant_name = collection_name
            database_name = collection_name

            # Check if tenant exists, create if not
            tenant_url = f"{self.base_url}/api/v2/tenants/{tenant_name}"
            tenant_response = self.session.get(tenant_url)
            if tenant_response.status_code != 200:
                logger.info(f"Tenant {tenant_name} does not exist, creating...")
                tenant_payload = {"name": tenant_name}
                tenant_create_url = f"{self.base_url}/api/v2/tenants"
                tenant_create_response = self.session.post(tenant_create_url, json=tenant_payload)
                if tenant_create_response.status_code in [200, 201]:
                    logger.info(f"Tenant {tenant_name} created successfully")
                else:
                    logger.error(f"Failed to create tenant: {tenant_create_response.status_code}, {tenant_create_response.text}")
                    return None

            # Check if database exists, create if not
            database_url = f"{self.base_url}/api/v2/tenants/{tenant_name}/databases/{database_name}"
            database_response = self.session.get(database_url)
            if database_response.status_code != 200:
                logger.info(f"Database {database_name} does not exist, creating...")
                database_payload = {"name": database_name}
                database_create_url = f"{self.base_url}/api/v2/tenants/{tenant_name}/databases"
                database_create_response = self.session.post(database_create_url, json=database_payload)
                if database_create_response.status_code in [200, 201]:
                    logger.info(f"Database {database_name} created successfully")
                else:
                    logger.error(f"Failed to create database: {database_create_response.status_code}, {database_create_response.text}")
                    return None

            # Get embedding dimension from the embeddings model first
            try:
                sample_embedding = self.embeddings.embed_query("test")
                expected_dimension = len(sample_embedding)
                logger.info(f"Expected embedding dimension: {expected_dimension}")
            except Exception as e:
                logger.warning(f"Could not detect embedding dimension: {e}, using default 768")
                expected_dimension = 768

            # Try to list collections to find our collection by name
            response = self.session.get(
                f"{self.base_url}/api/v2/tenants/{tenant_name}/databases/{database_name}/collections"
            )

            existing_collection_id = None
            if response.status_code == 200:
                collections = response.json()
                # Look for existing collection by name
                for collection in collections:
                    if collection.get("name") == collection_name:
                        existing_collection_id = collection.get("id")
                        logger.info(f"Found existing collection {collection_name} with ID: {existing_collection_id}")

                        # Check if we can get collection details to verify dimension
                        try:
                            # Try to get collection details
                            detail_response = self.session.get(
                                f"{self.base_url}/api/v2/tenants/{tenant_name}/databases/{database_name}/collections/{existing_collection_id}"
                            )
                            if detail_response.status_code == 200:
                                collection_details = detail_response.json()
                                stored_dimension = collection_details.get("metadata", {}).get("embedding_dimension")
                                if stored_dimension and int(stored_dimension) != expected_dimension:
                                    logger.warning(f"Collection has wrong dimension {stored_dimension}, expected {expected_dimension}")
                                    logger.warning(f"Deleting and recreating collection {collection_name}")

                                    # Delete the existing collection
                                    delete_response = self.session.delete(
                                        f"{self.base_url}/api/v2/tenants/{tenant_name}/databases/{database_name}/collections/{existing_collection_id}"
                                    )
                                    if delete_response.status_code in [200, 204]:
                                        logger.info(f"Deleted collection {collection_name}")
                                        existing_collection_id = None  # Force recreation
                                    else:
                                        logger.error(f"Failed to delete collection: {delete_response.status_code}, {delete_response.text}")
                                else:
                                    logger.info(f"Collection dimension is correct: {expected_dimension}")
                                    return existing_collection_id
                        except Exception as detail_error:
                            logger.warning(f"Could not verify collection dimension: {detail_error}")
                            # If we can't verify, assume it's correct and try to use it
                            return existing_collection_id

            # Collection doesn't exist or was deleted, create it with proper embedding dimension
            if not existing_collection_id:
                payload = {
                    "name": collection_name,
                    "metadata": {
                        "hnsw:space": "cosine",
                        "embedding_dimension": expected_dimension
                    },
                    "get_or_create": True
                }
                response = self.session.post(
                    f"{self.base_url}/api/v2/tenants/{tenant_name}/databases/{database_name}/collections",
                    json=payload
                )

                if response.status_code in [200, 201]:
                    collection_data = response.json()
                    collection_id = collection_data.get("id")
                    logger.info(f"Created collection {collection_name} with ID: {collection_id} and dimension: {expected_dimension}")
                    return collection_id
                else:
                    logger.error(f"Failed to create collection: {response.status_code}, {response.text}")
                    return None

            return existing_collection_id

        except Exception as e:
            logger.error(f"Error ensuring collection: {e}")
            return None

    def _add_to_chroma(self, documents: List[Document], embeddings_list: List[List[float]]) -> bool:
        """Add documents directly to ChromaDB using collection ID."""
        try:
            payload = {
                "ids": [str(uuid.uuid4()) for _ in documents],
                "embeddings": embeddings_list,
                "metadatas": [doc.metadata for doc in documents],
                "documents": [doc.page_content for doc in documents]
            }

            # Use collection ID instead of collection name
            response = self.session.post(
                f"{self.base_url}/api/v2/tenants/{self.tenant}/databases/{self.database}/collections/{self.collection_id}/add",
                json=payload
            )

            if response.status_code in (200, 201):
                logger.info(f"Successfully added {len(documents)} documents to ChromaDB collection {self.collection_id}")
                return True
            else:
                logger.error(f"Failed to add documents: {response.status_code}, {response.text}")
                return False
        except Exception as e:
            logger.error(f"Error adding documents to ChromaDB: {e}")
            return False

    def _query_chroma(self, query_embeddings: List[List[float]], n_results: int = 4) -> Optional[Dict]:
        """Query ChromaDB directly using collection ID."""
        try:
            payload = {
                "query_embeddings": query_embeddings,
                "n_results": n_results,
                "include": ["documents", "metadatas", "distances"]
            }

            # Use collection ID instead of collection name
            response = self.session.post(
                f"{self.base_url}/api/v2/tenants/{self.tenant}/databases/{self.database}/collections/{self.collection_id}/query",
                json=payload
            )

            if response.status_code in (200, 201):
                return response.json()
            else:
                logger.error(f"Failed to query ChromaDB: {response.status_code}, {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error querying ChromaDB: {e}")
            return None

    def _query_chroma(self, query_embeddings: List[List[float]], n_results: int = 4, where_filter: Optional[Dict] = None) -> Optional[Dict]:
        """Query ChromaDB directly using collection ID."""
        try:
            payload = {
                "query_embeddings": query_embeddings,
                "n_results": n_results,
                "include": ["documents", "metadatas", "distances"]
            }
            if where_filter:
                payload["where"] = where_filter
                logger.info(f"Querying ChromaDB with where_filter: {where_filter}")
            else:
                logger.info(f"Querying ChromaDB without where_filter for n_results: {n_results}")


            # Use collection ID instead of collection name
            response = self.session.post(
                f"{self.base_url}/api/v2/tenants/{self.tenant}/databases/{self.database}/collections/{self.collection_id}/query",
                json=payload
            )

            if response.status_code in (200, 201):
                return response.json()
            else:
                logger.error(f"Failed to query ChromaDB: {response.status_code}, {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error querying ChromaDB: {e}")
            return None

    # Public interface methods
    def create_from_documents(self, documents: List[Document]) -> None:
        """Create vector store from documents."""
        self.add_documents(documents)

    def add_documents(self, documents: List[Document]) -> None:
        """Add documents to the vector store."""
        try:
            # Filter metadata and generate embeddings
            filtered_documents = []
            embeddings_list = []

            for doc in documents:
                filtered_doc = Document(
                    page_content=doc.page_content,
                    metadata=filter_metadata(doc.metadata)
                )
                filtered_documents.append(filtered_doc)

                # Generate embedding
                embedding = self.embeddings.embed_query(doc.page_content)
                embeddings_list.append(embedding)

            # Add to ChromaDB
            if not self._add_to_chroma(filtered_documents, embeddings_list):
                raise RuntimeError("Failed to add documents to ChromaDB")

            logger.info(f"Added {len(documents)} documents to vector store")
        except Exception as e:
            logger.error(f"Error adding documents: {e}")
            raise

    def similarity_search(self, query: str, k: int = 4, user_id: Optional[str] = None, accessible_docs_set: Optional[Set[str]] = None) -> List[Document]:
        """Perform similarity search using external ChromaDB with native filtering."""
        try:
            query_embedding = self.embeddings.embed_query(query)
            
            final_accessible_docs_set = accessible_docs_set
            # Determine accessible documents if user_id is provided and set isn't
            if user_id and final_accessible_docs_set is None: # Check for None specifically
                from app.auth.user import UserManager
                user_manager_instance = UserManager() # Get an instance
                final_accessible_docs_set = user_manager_instance.get_accessible_documents(user_id)
                logger.info(f"User {user_id}: determined accessible_docs_set with {len(final_accessible_docs_set)} docs. Set: {final_accessible_docs_set}")


            where_filter = None
            # If final_accessible_docs_set is not None, it means access control should be applied.
            # An empty set means the user has access to NO documents.
            if final_accessible_docs_set is not None:
                if not final_accessible_docs_set:  # User has access to no documents
                    logger.info(f"User {user_id} has no accessible documents. Returning empty list from similarity_search.")
                    return []
                # Ensure document_id is a valid metadata field for filtering
                where_filter = {"document_id": {"$in": list(final_accessible_docs_set)}}
            
            logger.info(f"Similarity search for user {user_id}, query '{query[:50]}...'. Where filter: {where_filter}")

            # Query ChromaDB with or without the where_filter
            # The number of results 'k' is requested directly.
            results = self._query_chroma([query_embedding], n_results=k, where_filter=where_filter)
            
            documents = []
            if results and 'documents' in results and results['documents'] and results['documents'][0] is not None:
                for i, doc_text in enumerate(results['documents'][0]):
                    metadata = results['metadatas'][0][i] if 'metadatas' in results and results['metadatas'] and results['metadatas'][0] is not None else {}
                    distance = results['distances'][0][i] if 'distances' in results and results['distances'] and results['distances'][0] is not None else 0.0
                    metadata['score'] = 1.0 - distance if distance <= 1.0 else 0.0
                    documents.append(Document(page_content=doc_text, metadata=metadata))
            
            logger.info(f"Retrieved {len(documents)} documents for query: '{query[:50]}...' with filter: {where_filter is not None} for user: {user_id}")
            return documents
        except Exception as e:
            logger.error(f"Error in similarity search for user {user_id} and query '{query[:50]}...': {str(e)}", exc_info=True)
            # Re-raise to allow DocumentRetriever to handle or log it
            raise

    def similarity_search_with_score(self, query: str, k: int = 4, user_id: Optional[str] = None, accessible_docs_set: Optional[Set[str]] = None, **kwargs) -> List[tuple]:
        """Perform similarity search with native filtering and return (Document, score) tuples."""
        try:
            query_embedding = self.embeddings.embed_query(query)

            final_accessible_docs_set = accessible_docs_set
            if user_id and final_accessible_docs_set is None: # Check for None specifically
                from app.auth.user import UserManager
                user_manager_instance = UserManager() # Get an instance
                final_accessible_docs_set = user_manager_instance.get_accessible_documents(user_id)
                logger.info(f"User {user_id}: determined accessible_docs_set with {len(final_accessible_docs_set)} docs for similarity_search_with_score. Set: {final_accessible_docs_set}")

            where_filter = None
            if final_accessible_docs_set is not None:
                if not final_accessible_docs_set:
                    logger.info(f"User {user_id} has no accessible documents. Returning empty list from similarity_search_with_score.")
                    return []
                where_filter = {"document_id": {"$in": list(final_accessible_docs_set)}}

            logger.info(f"Similarity search with score for user {user_id}, query '{query[:50]}...'. Where filter: {where_filter}")

            results = self._query_chroma([query_embedding], n_results=k, where_filter=where_filter)
            
            documents_with_scores = []
            if results and 'documents' in results and results['documents'] and results['documents'][0] is not None:
                for i, doc_text in enumerate(results['documents'][0]):
                    metadata = results['metadatas'][0][i] if 'metadatas' in results and results['metadatas'] and results['metadatas'][0] is not None else {}
                    distance = results['distances'][0][i] if 'distances' in results and results['distances'] and results['distances'][0] is not None else 0.0
                    score = 1.0 - distance if distance <= 1.0 else 0.0
                    doc = Document(page_content=doc_text, metadata=metadata)
                    documents_with_scores.append((doc, score))
            
            logger.info(f"Retrieved {len(documents_with_scores)} documents with scores for query: '{query[:50]}...' with filter: {where_filter is not None} for user: {user_id}")
            return documents_with_scores
        except Exception as e:
            logger.error(f"Error in similarity_search_with_score for user {user_id} and query '{query[:50]}...': {str(e)}", exc_info=True)
            return [] # Keep returning empty list on error for this one

    def as_retriever(self, **kwargs):
        """Return a retriever interface compatible with langchain."""
        # Store self (the VectorStore instance) to be accessed by ChromaRetriever
        # This allows ChromaRetriever to access self.user_id and self.similarity_search
        parent_vector_store = self

        class ChromaRetriever:
            def __init__(self, vector_store_instance): # vector_store_instance is the parent_vector_store
                self.vector_store = vector_store_instance # This is the VectorStore object

            def get_relevant_documents(self, query: str, **kwargs) -> List[Document]:
                k = kwargs.get('k', 4)
                # Access user_id from the parent VectorStore instance
                user_id_for_search = self.vector_store.user_id
                # Call similarity_search on the parent VectorStore instance, passing the user_id
                return self.vector_store.similarity_search(query, k=k, user_id=user_id_for_search)

            # _get_relevant_documents is often called by Langchain internals
            def _get_relevant_documents(self, query: str, run_manager=None, **kwargs) -> List[Document]: # Added run_manager for compatibility
                # Ensure k is passed if present in kwargs, otherwise default
                k_val = kwargs.get('k', 4) # Default k to 4 if not provided
                user_id_for_search = self.vector_store.user_id
                return self.vector_store.similarity_search(query, k=k_val, user_id=user_id_for_search)

        return ChromaRetriever(parent_vector_store) # Pass self (VectorStore instance)

    def delete_document(self, document_id: str) -> bool:
        """Delete a document from ChromaDB (not implemented)."""
        logger.warning(f"Document deletion not implemented for document {document_id}")
        return False

    def reset(self) -> bool:
        """Reset the vector store by recreating the collection."""
        try:
            collection_id = self._ensure_collection()
            if collection_id:
                self.collection_id = collection_id
                logger.info(f"Reset ChromaDB vector store with collection: {self.collection_name} (ID: {self.collection_id})")
                return True
            return False
        except Exception as e:
            logger.error(f"Error resetting vector store: {e}")
            return False

    def save(self, save_path: Optional[str] = None) -> None:
        """Save the vector store (ChromaDB is automatically persisted).

        Args:
            save_path: Ignored - kept for backward compatibility
        """
        logger.info("External ChromaDB vector store is automatically persisted")

    @classmethod
    def load(cls, embeddings: Embeddings, user_id: Optional[str] = None,
             load_path: Optional[str] = None, store_type: str = "chroma", **kwargs) -> 'VectorStore':
        """Load/initialize the external ChromaDB vector store.

        Args:
            embeddings: The embeddings instance to use
            user_id: The ID of the user who owns this vector store
            load_path: Ignored - kept for backward compatibility
            store_type: Ignored - kept for backward compatibility
            **kwargs: Additional arguments for backward compatibility
        """
        return cls(embeddings=embeddings, user_id=user_id)
