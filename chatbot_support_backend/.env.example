# =============================================================================
# CHATBOT SUPPORT BACKEND - ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and fill in your actual values
# DO NOT commit .env file to version control!

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
# Set to 'production' to use real AWS S3, 'local' for LocalStack
ENV=production

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# MySQL database settings
DB_HOST=db
DB_PORT=3306
DB_USER=raguser
DB_PASSWORD=your_secure_database_password_here
DB_NAME=ragchatbot
DB_EXTERNAL_PORT=3306
DATABASE_LOGGING=false

# AWS RDS Secrets Manager (for production deployments)
# DB_SECRET_ARN=arn:aws:secretsmanager:region:account:secret:name

# =============================================================================
# AWS S3 CONFIGURATION (REQUIRED)
# =============================================================================
# Your AWS credentials - NEVER commit these to version control!
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1

# S3 bucket for document storage
S3_BUCKET_NAME=your-chatbot-documents-bucket
S3_PREFIX=chatbot_support

# =============================================================================
# GOOGLE AI CONFIGURATION (REQUIRED for AI features)
# =============================================================================
# Google Cloud Project ID
GOOGLE_PROJECT_ID=your-google-cloud-project-id

# Option 1: Service Account File (RECOMMENDED)
# Path to your Google service account JSON file
GOOGLE_CREDENTIALS_FILE=./google_credentials.json

# Option 2: API Key (alternative to service account)
# GOOGLE_API_KEY=your_google_api_key_here

# =============================================================================
# JWT AUTHENTICATION CONFIGURATION
# =============================================================================
# IMPORTANT: Change this to a strong, random secret key in production!
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# =============================================================================
# VECTOR STORE CONFIGURATION
# =============================================================================
VECTOR_STORE_TYPE=chroma
VECTOR_STORE_DIR=/app/central_vector_store
USER_DATA_DIR=/app/user_data

# External ChromaDB Configuration (for production)
# Set USE_EXTERNAL_CHROMA=true to use external ChromaDB server
USE_EXTERNAL_CHROMA=false
CHROMA_HOST=localhost
CHROMA_PORT=8000
CHROMA_TENANT=support_chatbot
CHROMA_DATABASE=support_chatbot
CHROMA_COLLECTION_NAME=support_chatbot

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Allowed origins for CORS (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,https://your-frontend-domain.com

# =============================================================================
# LOGGING AND DEBUGGING
# =============================================================================
LOG_LEVEL=INFO

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================
# 1. Copy this file to .env: cp .env.example .env
# 2. Fill in all the required values above
# 3. Create your Google service account JSON file and place it as google_credentials.json
# 4. Create your S3 bucket in AWS
# 5. Run: docker-compose up -d

# =============================================================================
# SECURITY NOTES
# =============================================================================
# - Never commit .env file to version control
# - Use strong, unique passwords and secret keys
# - Rotate credentials regularly
# - Use IAM roles in production instead of access keys when possible
# - Enable S3 bucket encryption and versioning
# - Use AWS Secrets Manager for production database credentials
