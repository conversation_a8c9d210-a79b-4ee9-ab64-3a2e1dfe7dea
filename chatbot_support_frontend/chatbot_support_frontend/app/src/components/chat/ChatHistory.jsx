import React, { useState, useEffect } from 'react';
import {
  PlusIcon,
  ChatBubbleLeftRightIcon,
  UserCircleIcon,
  ChevronDownIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon,
  TrashIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { useChat } from '../../contexts/ChatContext';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import './ChatHistory.css';

const ChatHistory = () => {
  // Get theme from context
  const theme = useTheme();
  const {
    conversations,
    loadConversation,
    startNewConversation,
    currentConversationId,
    formatTimestamp,
    fetchConversations,
    deleteConversation
  } = useChat();

  // Get auth context to check if user is admin
  const { user } = useAuth();

  // State for pagination
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const ITEMS_PER_PAGE = 4; // Show only 4 chats initially

  // State for delete confirmation
  const [deleteConfirmId, setDeleteConfirmId] = useState(null);

  // Format date for display with real-time context
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();

    // Check if the date is today
    if (date.toDateString() === now.toDateString()) {
      return 'Today';
    }

    // Check if the date is yesterday
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    }

    // Check if the date is within the last 7 days
    const lastWeek = new Date(now);
    lastWeek.setDate(now.getDate() - 7);
    if (date > lastWeek) {
      return 'Last 7 days';
    }

    // For older dates, show the month and day (and year if different)
    return date.toLocaleDateString(undefined, {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  };

  // Group conversations by date
  const groupedConversations = conversations.reduce((groups, conversation) => {
    const date = new Date(conversation.created_at).toLocaleDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(conversation);
    return groups;
  }, {});

  // Function to load more conversations
  const loadMoreConversations = async () => {
    setIsLoading(true);
    try {
      const newConversations = await fetchConversations(page + 1);
      setPage(page + 1);

      // Scroll to show new conversations
      setTimeout(() => {
        const chatList = document.querySelector('.chat-history-list');
        if (chatList) {
          const scrollPosition = chatList.scrollTop;
          // Scroll down a bit to show new content was loaded
          chatList.scrollTo({
            top: scrollPosition + 100,
            behavior: 'smooth'
          });
        }
      }, 100);

      return newConversations;
    } catch (error) {
      console.error('Error loading more conversations:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  // Get paginated conversations
  const paginatedConversations = conversations.slice(0, page * ITEMS_PER_PAGE);

  // Group the paginated conversations
  const paginatedGroupedConversations = paginatedConversations.reduce((groups, conversation) => {
    const date = new Date(conversation.created_at).toLocaleDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(conversation);
    return groups;
  }, {});

  // Check if there are more conversations to load
  const hasMoreConversations = conversations.length > page * ITEMS_PER_PAGE;

  // Get the parent element class to check if we're in admin page
  const isAdminPage = document.querySelector('.admin-page') !== null;

  return (
    <div className={`chat-history ${isAdminPage ? 'in-admin-page' : ''}`} style={isAdminPage ? { height: 'calc(100vh - 65px)' } : {}}>
      <div className="chat-history-header">
        <div className="chat-history-title">
          <h3>Chat History</h3>
        </div>
        <button
          className="new-chat-button"
          onClick={startNewConversation}
        >
          <PlusIcon className="new-chat-icon" />
          New Chat
        </button>
      </div>

      <div className="chat-history-list">
        {Object.keys(paginatedGroupedConversations).length === 0 ? (
          <div className="no-conversations">
            <p>No previous conversations</p>
          </div>
        ) : (
          <>
            {Object.entries(paginatedGroupedConversations)
              .sort(([dateA], [dateB]) => new Date(dateB) - new Date(dateA))
              .map(([date, convos]) => (
                <div key={date} className="conversation-group">
                  <div className="date-header">
                    {formatDate(date)}
                  </div>
                  {convos.map(conversation => (
                    <div
                      key={conversation.id}
                      className={`conversation-item ${currentConversationId === conversation.id ? 'active' : ''}`}
                    >
                      <div
                        className="conversation-content"
                        onClick={() => loadConversation(conversation.id)}
                      >
                        <ChatBubbleLeftRightIcon className="conversation-icon" />
                        <div className="conversation-details">
                          <div className="conversation-title">
                            {conversation.title || `Conversation on ${new Date(conversation.created_at).toLocaleDateString()} at ${new Date(conversation.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`}
                          </div>
                          <div className="conversation-meta">
                            {user?.role === 'admin' && conversation.username && (
                              <div className="conversation-username">
                                <UserCircleIcon className="username-icon" />
                                {conversation.username}
                              </div>
                            )}
                            <div className="message-count">
                              {conversation.message_count} messages
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Delete button and confirmation */}
                      {deleteConfirmId === conversation.id ? (
                        <div className="delete-confirm">
                          <button
                            className="delete-yes"
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteConversation(conversation.id);
                              setDeleteConfirmId(null);
                            }}
                            title="Confirm delete"
                          >
                            <TrashIcon className="delete-icon" />
                          </button>
                          <button
                            className="delete-no"
                            onClick={(e) => {
                              e.stopPropagation();
                              setDeleteConfirmId(null);
                            }}
                            title="Cancel"
                          >
                            <XMarkIcon className="cancel-icon" />
                          </button>
                        </div>
                      ) : (
                        <button
                          className="delete-button"
                          onClick={(e) => {
                            e.stopPropagation();
                            setDeleteConfirmId(conversation.id);
                          }}
                          title="Delete conversation"
                        >
                          <TrashIcon className="delete-icon" />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              ))}

            {hasMoreConversations && (
              <div className="load-more-container">
                <button
                  className="load-more-button"
                  onClick={loadMoreConversations}
                  disabled={isLoading}
                >
                  {isLoading ? 'Loading...' : (
                    <>
                      Load More ({conversations.length - paginatedConversations.length} remaining)
                      <ChevronDownIcon className="load-more-icon" />
                    </>
                  )}
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ChatHistory;
