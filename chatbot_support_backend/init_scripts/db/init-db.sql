-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS ragchatbot;
USE ragchatbot;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- Create groups table
CREATE TABLE IF NOT EXISTS `groups` (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT
);

-- Create user_groups table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS user_groups (
    user_id VARCHAR(36) NOT NULL,
    group_id VARCHAR(36) NOT NULL,
    PRIMARY KEY (user_id, group_id),
    FOR<PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (group_id) REFERENCES `groups`(id) ON DELETE CASCADE
);

-- Create documents table with all fields including migrations
CREATE TABLE IF NOT EXISTS documents (
    id VARCHAR(36) PRIMARY KEY,
    document_number VARCHAR(20) UNIQUE,  -- Added in add_document_number.py migration
    filename VARCHAR(255) NOT NULL,
    s3_key VARCHAR(255) NOT NULL,
    content_type VARCHAR(100) NOT NULL,
    size BIGINT NOT NULL,
    uploaded_by VARCHAR(36) NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMP NULL,
    -- Added in add_document_metadata_fields.py migration
    service_name VARCHAR(255),
    software_menus VARCHAR(255),
    issue_type VARCHAR(255),
    FOREIGN KEY (uploaded_by) REFERENCES users(id)
);

-- Create document_access table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS document_access (
    document_id VARCHAR(36) NOT NULL,
    group_id VARCHAR(36) NOT NULL,
    PRIMARY KEY (document_id, group_id),
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES `groups`(id) ON DELETE CASCADE
);

-- Create embeddings table
CREATE TABLE IF NOT EXISTS embeddings (
    id VARCHAR(36) PRIMARY KEY,
    document_id VARCHAR(36) NOT NULL,
    chunk_index INT NOT NULL,
    chunk_text TEXT NOT NULL,
    embedding_vector LONGTEXT NOT NULL,  -- Store as base64 encoded string
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
);

-- Create conversations table with title field (added in add_title_to_conversations.py)
CREATE TABLE IF NOT EXISTS conversations (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    title VARCHAR(255),  -- Added in add_title_to_conversations.py migration
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create messages table with user_id field (added in add_user_id_to_messages.py)
CREATE TABLE IF NOT EXISTS messages (
    id VARCHAR(36) PRIMARY KEY,
    conversation_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36),  -- Added in add_user_id_to_messages.py migration
    role VARCHAR(50) NOT NULL,  -- 'user', 'assistant', or 'system'
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE  -- Added in add_user_id_to_messages.py migration
);

-- Create chatbot_configs table
CREATE TABLE IF NOT EXISTS chatbot_configs (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    use_openai_embeddings BOOLEAN DEFAULT FALSE,
    use_google_embeddings BOOLEAN DEFAULT FALSE,
    use_google_llm BOOLEAN DEFAULT FALSE,
    openai_api_key VARCHAR(255),
    google_api_key VARCHAR(255),
    google_service_account_file VARCHAR(255),
    google_project_id VARCHAR(255),
    embedding_model VARCHAR(255) DEFAULT 'all-MiniLM-L6-v2',
    llm_model_name VARCHAR(255) DEFAULT 'gemini-2.0-flash-lite',
    vector_store_type VARCHAR(50) DEFAULT 'chroma',
    vector_store_dir VARCHAR(255) NOT NULL,
    use_compression BOOLEAN DEFAULT FALSE,
    top_k INT DEFAULT 4,
    max_memory_messages INT DEFAULT 10,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create notifications table (added in fix_notifications_table.py)
CREATE TABLE IF NOT EXISTS notifications (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36),  -- Target user (null for all users)
    created_by VARCHAR(36),  -- User who triggered the notification
    document_id VARCHAR(36),  -- Related document if any
    type VARCHAR(50) NOT NULL,  -- Type of notification: 'document_added', 'document_deleted', 'user_added', etc.
    title VARCHAR(255) NOT NULL,  -- Short title for the notification
    message TEXT NOT NULL,  -- Detailed message
    is_read BOOLEAN DEFAULT FALSE,  -- Whether the notification has been read
    is_global BOOLEAN DEFAULT FALSE,  -- Whether this is a global notification for all users
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE SET NULL
);

-- Create notification_read_status table (added in add_notification_read_status_table.py)
CREATE TABLE IF NOT EXISTS notification_read_status (
    id VARCHAR(36) PRIMARY KEY,
    notification_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uix_notification_user (notification_id, user_id)
);

-- Create user_activities table
CREATE TABLE IF NOT EXISTS user_activities (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    document_id VARCHAR(36),
    action VARCHAR(50) NOT NULL,  -- 'document_view', 'document_upload', 'chat_message', etc.
    details TEXT,  -- Additional details as JSON
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE SET NULL
);

-- Create service_name_options table (added in add_dropdown_options_tables.py)
CREATE TABLE IF NOT EXISTS service_name_options (
    id VARCHAR(36) PRIMARY KEY,
    value VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(36),
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Create software_menu_options table (added in add_dropdown_options_tables.py)
CREATE TABLE IF NOT EXISTS software_menu_options (
    id VARCHAR(36) PRIMARY KEY,
    value VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(36),
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Create issue_type_options table (added in add_dropdown_options_tables.py)
CREATE TABLE IF NOT EXISTS issue_type_options (
    id VARCHAR(36) PRIMARY KEY,
    value VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(36),
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert default admin user (password: admin)
INSERT INTO users (id, username, password_hash, role)
VALUES (UUID(), 'admin', '8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918', 'admin')
ON DUPLICATE KEY UPDATE username = username;

-- Get the admin user ID
SET @admin_id = (SELECT id FROM users WHERE username = 'admin');

-- Insert default group
INSERT INTO `groups` (id, name, description)
VALUES (UUID(), 'Administrators', 'Administrator group with full access')
ON DUPLICATE KEY UPDATE name = name;

-- Get the admin group ID
SET @admin_group_id = (SELECT id FROM `groups` WHERE name = 'Administrators');

-- Add admin user to admin group
INSERT INTO user_groups (user_id, group_id)
VALUES (@admin_id, @admin_group_id)
ON DUPLICATE KEY UPDATE user_id = user_id;

-- Insert default dropdown options (from add_dropdown_options_tables.py)
-- Default service name options
INSERT INTO service_name_options (id, value, created_by)
VALUES
(UUID(), 'Customer Support', @admin_id),
(UUID(), 'Technical Support', @admin_id),
(UUID(), 'Sales', @admin_id),
(UUID(), 'Billing', @admin_id),
(UUID(), 'Other', @admin_id)
ON DUPLICATE KEY UPDATE value = value;

-- Default software menu options
INSERT INTO software_menu_options (id, value, created_by)
VALUES
(UUID(), 'Dashboard', @admin_id),
(UUID(), 'Reports', @admin_id),
(UUID(), 'Settings', @admin_id),
(UUID(), 'User Management', @admin_id),
(UUID(), 'Other', @admin_id)
ON DUPLICATE KEY UPDATE value = value;

-- Default issue type options
INSERT INTO issue_type_options (id, value, created_by)
VALUES
(UUID(), 'Bug', @admin_id),
(UUID(), 'Feature Request', @admin_id),
(UUID(), 'Question', @admin_id),
(UUID(), 'Documentation', @admin_id),
(UUID(), 'Other', @admin_id)
ON DUPLICATE KEY UPDATE value = value;

-- Create processed_documents table for tracking document processing status
CREATE TABLE IF NOT EXISTS processed_documents (
    document_id VARCHAR(36) PRIMARY KEY,
    file_hash VARCHAR(32) NOT NULL,
    last_processed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    embedding_count INT NOT NULL,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
);
