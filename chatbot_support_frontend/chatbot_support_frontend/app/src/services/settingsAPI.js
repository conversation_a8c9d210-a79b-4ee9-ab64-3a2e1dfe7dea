// Import from main API service
import { API_BASE_URL, getAuthToken, handleResponse } from './api';

// Settings API
export const settingsAPI = {

  // Clear conversation history
  clearConversation: async () => {
    const response = await fetch(`${API_BASE_URL}/clear`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json',
      },
    });

    return handleResponse(response);
  },

  // Create a new conversation
  createNewConversation: async (title = null) => {
    try {
      const response = await fetch(`${API_BASE_URL}/conversations/new${title ? `?title=${encodeURIComponent(title)}` : ''}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      // Log the response status for debugging
      console.log('Create conversation response status:', response.status);

      return await handleResponse(response);
    } catch (error) {
      console.error('Error in createNewConversation API call:', error);
      throw error;
    }
  },

  // Save vector store
  saveVectorStore: async () => {
    const response = await fetch(`${API_BASE_URL}/save`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json',
      },
    });

    return handleResponse(response);
  },

  // Process all documents in data folder
  processDataFolder: async () => {
    const response = await fetch(`${API_BASE_URL}/process-data-folder`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json',
      },
    });

    return handleResponse(response);
  },

  // Hard reset - delete all documents and embeddings
  hardReset: async () => {
    const response = await fetch(`${API_BASE_URL}/hard-reset`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json',
      },
    });

    return handleResponse(response);
  },

  // Get conversations
  getConversations: async (page = 1, limit = 20, includeEmpty = false) => {
    const response = await fetch(`${API_BASE_URL}/conversations?page=${page}&limit=${limit}&include_empty=${includeEmpty}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json',
      },
    });

    return handleResponse(response);
  },

  // Get conversation messages
  getConversationMessages: async (conversationId) => {
    const response = await fetch(`${API_BASE_URL}/conversations/${conversationId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json',
      },
    });

    return handleResponse(response);
  },

  // Delete a conversation
  deleteConversation: async (conversationId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/conversations/${conversationId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      // Log the response status for debugging
      console.log('Delete conversation response status:', response.status);

      return await handleResponse(response);
    } catch (error) {
      console.error('Error in deleteConversation API call:', error);
      throw error;
    }
  },
};
